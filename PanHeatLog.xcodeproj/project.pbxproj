// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		28474D852E10E07000569E15 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 28474D7C2E10E07000569E15 /* Assets.xcassets */; };
		28474D872E10E07000569E15 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 28474D7F2E10E07000569E15 /* LaunchScreen.storyboard */; };
		28474D892E10E07000569E15 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474D7B2E10E07000569E15 /* AppDelegate.swift */; };
		28474D942E10E29600569E15 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474D8F2E10E29600569E15 /* SceneDelegate.swift */; };
		28474DD12E10E96700569E15 /* BaseButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DC22E10E96700569E15 /* BaseButton.swift */; };
		28474DD22E10E96700569E15 /* AppTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DC82E10E96700569E15 /* AppTheme.swift */; };
		28474DD32E10E96700569E15 /* HeatScanRecordCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DCF2E10E96700569E15 /* HeatScanRecordCell.swift */; };
		28474DD42E10E96700569E15 /* HeatScanListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DCD2E10E96700569E15 /* HeatScanListViewController.swift */; };
		28474DD52E10E96700569E15 /* HeatScanDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DCC2E10E96700569E15 /* HeatScanDetailViewController.swift */; };
		28474DD62E10E96700569E15 /* HeatScanRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DC62E10E96700569E15 /* HeatScanRecord.swift */; };
		28474DD72E10E96700569E15 /* FilterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DCB2E10E96700569E15 /* FilterViewController.swift */; };
		28474DD82E10E96700569E15 /* HeatScanDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DC52E10E96700569E15 /* HeatScanDataManager.swift */; };
		28474DD92E10E96700569E15 /* AddHeatScanViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DCA2E10E96700569E15 /* AddHeatScanViewController.swift */; };
		28474DDA2E10E96700569E15 /* BaseTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474DC32E10E96700569E15 /* BaseTextField.swift */; };
		D24881731D5E6E0F144F02E5 /* Pods_PanHeatLog.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C9C64ED3F847CA2706CDB00B /* Pods_PanHeatLog.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		28474D632E10E06B00569E15 /* PanHeatLog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PanHeatLog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		28474D7B2E10E07000569E15 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		28474D7C2E10E07000569E15 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		28474D7D2E10E07000569E15 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		28474D7E2E10E07000569E15 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		28474D8F2E10E29600569E15 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		28474DC22E10E96700569E15 /* BaseButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseButton.swift; sourceTree = "<group>"; };
		28474DC32E10E96700569E15 /* BaseTextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseTextField.swift; sourceTree = "<group>"; };
		28474DC52E10E96700569E15 /* HeatScanDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeatScanDataManager.swift; sourceTree = "<group>"; };
		28474DC62E10E96700569E15 /* HeatScanRecord.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeatScanRecord.swift; sourceTree = "<group>"; };
		28474DC82E10E96700569E15 /* AppTheme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppTheme.swift; sourceTree = "<group>"; };
		28474DCA2E10E96700569E15 /* AddHeatScanViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddHeatScanViewController.swift; sourceTree = "<group>"; };
		28474DCB2E10E96700569E15 /* FilterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterViewController.swift; sourceTree = "<group>"; };
		28474DCC2E10E96700569E15 /* HeatScanDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeatScanDetailViewController.swift; sourceTree = "<group>"; };
		28474DCD2E10E96700569E15 /* HeatScanListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeatScanListViewController.swift; sourceTree = "<group>"; };
		28474DCF2E10E96700569E15 /* HeatScanRecordCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeatScanRecordCell.swift; sourceTree = "<group>"; };
		AB17FC701FDC27DF408E67B1 /* Pods-PanHeatLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PanHeatLog.debug.xcconfig"; path = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog.debug.xcconfig"; sourceTree = "<group>"; };
		C9C64ED3F847CA2706CDB00B /* Pods_PanHeatLog.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PanHeatLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F5A49483B076877569A2FDAF /* Pods-PanHeatLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PanHeatLog.release.xcconfig"; path = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		28474D602E10E06B00569E15 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D24881731D5E6E0F144F02E5 /* Pods_PanHeatLog.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		015EC17D19C46E9924D9A865 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C9C64ED3F847CA2706CDB00B /* Pods_PanHeatLog.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		28474D5A2E10E06B00569E15 = {
			isa = PBXGroup;
			children = (
				28474D842E10E07000569E15 /* PanHeatLog */,
				28474D642E10E06B00569E15 /* Products */,
				F0997B05CE88BD43C73C2A60 /* Pods */,
				015EC17D19C46E9924D9A865 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		28474D642E10E06B00569E15 /* Products */ = {
			isa = PBXGroup;
			children = (
				28474D632E10E06B00569E15 /* PanHeatLog.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		28474D842E10E07000569E15 /* PanHeatLog */ = {
			isa = PBXGroup;
			children = (
				28474DC42E10E96700569E15 /* Components */,
				28474DC72E10E96700569E15 /* Models */,
				28474DC92E10E96700569E15 /* Theme */,
				28474DCE2E10E96700569E15 /* ViewControllers */,
				28474DD02E10E96700569E15 /* Views */,
				28474D8F2E10E29600569E15 /* SceneDelegate.swift */,
				28474D7B2E10E07000569E15 /* AppDelegate.swift */,
				28474D7C2E10E07000569E15 /* Assets.xcassets */,
				28474D7D2E10E07000569E15 /* Info.plist */,
				28474D7F2E10E07000569E15 /* LaunchScreen.storyboard */,
			);
			path = PanHeatLog;
			sourceTree = "<group>";
		};
		28474DC42E10E96700569E15 /* Components */ = {
			isa = PBXGroup;
			children = (
				28474DC22E10E96700569E15 /* BaseButton.swift */,
				28474DC32E10E96700569E15 /* BaseTextField.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		28474DC72E10E96700569E15 /* Models */ = {
			isa = PBXGroup;
			children = (
				28474DC52E10E96700569E15 /* HeatScanDataManager.swift */,
				28474DC62E10E96700569E15 /* HeatScanRecord.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		28474DC92E10E96700569E15 /* Theme */ = {
			isa = PBXGroup;
			children = (
				28474DC82E10E96700569E15 /* AppTheme.swift */,
			);
			path = Theme;
			sourceTree = "<group>";
		};
		28474DCE2E10E96700569E15 /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				28474DCA2E10E96700569E15 /* AddHeatScanViewController.swift */,
				28474DCB2E10E96700569E15 /* FilterViewController.swift */,
				28474DCC2E10E96700569E15 /* HeatScanDetailViewController.swift */,
				28474DCD2E10E96700569E15 /* HeatScanListViewController.swift */,
			);
			path = ViewControllers;
			sourceTree = "<group>";
		};
		28474DD02E10E96700569E15 /* Views */ = {
			isa = PBXGroup;
			children = (
				28474DCF2E10E96700569E15 /* HeatScanRecordCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		F0997B05CE88BD43C73C2A60 /* Pods */ = {
			isa = PBXGroup;
			children = (
				AB17FC701FDC27DF408E67B1 /* Pods-PanHeatLog.debug.xcconfig */,
				F5A49483B076877569A2FDAF /* Pods-PanHeatLog.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		28474D622E10E06B00569E15 /* PanHeatLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 28474D762E10E06D00569E15 /* Build configuration list for PBXNativeTarget "PanHeatLog" */;
			buildPhases = (
				12B8EB07CF282AF00D7761C7 /* [CP] Check Pods Manifest.lock */,
				28474D5F2E10E06B00569E15 /* Sources */,
				28474D602E10E06B00569E15 /* Frameworks */,
				28474D612E10E06B00569E15 /* Resources */,
				E4226D6850297015F9886B37 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PanHeatLog;
			productName = PanHeatLog;
			productReference = 28474D632E10E06B00569E15 /* PanHeatLog.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		28474D5B2E10E06B00569E15 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					28474D622E10E06B00569E15 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 28474D5E2E10E06B00569E15 /* Build configuration list for PBXProject "PanHeatLog" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 28474D5A2E10E06B00569E15;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 28474D642E10E06B00569E15 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				28474D622E10E06B00569E15 /* PanHeatLog */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		28474D612E10E06B00569E15 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28474D852E10E07000569E15 /* Assets.xcassets in Resources */,
				28474D872E10E07000569E15 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		12B8EB07CF282AF00D7761C7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PanHeatLog-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E4226D6850297015F9886B37 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		28474D5F2E10E06B00569E15 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28474D942E10E29600569E15 /* SceneDelegate.swift in Sources */,
				28474D892E10E07000569E15 /* AppDelegate.swift in Sources */,
				28474DD12E10E96700569E15 /* BaseButton.swift in Sources */,
				28474DD22E10E96700569E15 /* AppTheme.swift in Sources */,
				28474DD32E10E96700569E15 /* HeatScanRecordCell.swift in Sources */,
				28474DD42E10E96700569E15 /* HeatScanListViewController.swift in Sources */,
				28474DD52E10E96700569E15 /* HeatScanDetailViewController.swift in Sources */,
				28474DD62E10E96700569E15 /* HeatScanRecord.swift in Sources */,
				28474DD72E10E96700569E15 /* FilterViewController.swift in Sources */,
				28474DD82E10E96700569E15 /* HeatScanDataManager.swift in Sources */,
				28474DD92E10E96700569E15 /* AddHeatScanViewController.swift in Sources */,
				28474DDA2E10E96700569E15 /* BaseTextField.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		28474D7F2E10E07000569E15 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				28474D7E2E10E07000569E15 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		28474D772E10E06D00569E15 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AB17FC701FDC27DF408E67B1 /* Pods-PanHeatLog.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PanHeatLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.PanHeatLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		28474D782E10E06D00569E15 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F5A49483B076877569A2FDAF /* Pods-PanHeatLog.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PanHeatLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.PanHeatLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		28474D792E10E06D00569E15 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		28474D7A2E10E06D00569E15 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		28474D5E2E10E06B00569E15 /* Build configuration list for PBXProject "PanHeatLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28474D792E10E06D00569E15 /* Debug */,
				28474D7A2E10E06D00569E15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		28474D762E10E06D00569E15 /* Build configuration list for PBXNativeTarget "PanHeatLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28474D772E10E06D00569E15 /* Debug */,
				28474D782E10E06D00569E15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 28474D5B2E10E06B00569E15 /* Project object */;
}
