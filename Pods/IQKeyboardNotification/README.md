# IQKeyboardNotification
Lightweight library to observe keyboard events with ease.

[![CI Status](https://img.shields.io/travis/hackiftekhar/IQKeyboardNotification.svg?style=flat)](https://travis-ci.org/hackiftekhar/IQKeyboardNotification)
[![Version](https://img.shields.io/cocoapods/v/IQKeyboardNotification.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardNotification)
[![License](https://img.shields.io/cocoapods/l/IQKeyboardNotification.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardNotification)
[![Platform](https://img.shields.io/cocoapods/p/IQKeyboardNotification.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardNotification)

![Screenshot](https://raw.githubusercontent.com/hackiftekhar/IQKeyboardNotification/master/Screenshot/IQKeyboardNotificationScreenshot.png)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

IQKeyboardNotification is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'IQKeyboardNotification'
```

## Author

<NAME_EMAIL>

## Flow

![Screenshot](https://raw.githubusercontent.com/hackiftekhar/IQKeyboardNotification/master/Screenshot/FlowDiagram.jpg)

## License

IQKeyboardNotification is available under the MIT license. See the LICENSE file for more info.
