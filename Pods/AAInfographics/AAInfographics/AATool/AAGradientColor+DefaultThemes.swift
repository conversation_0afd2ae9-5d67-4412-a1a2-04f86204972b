//
//  AAGradientColor+DefaultThemes.swift
//  AAInfographics
//
//  Created by AnAn on 2024/2/5.
//  Copyright © 2024 An An. All rights reserved.
//*************** ...... SOURCE CODE ...... ***************
//***...................................................***
//*** https://github.com/AAChartModel/AAChartKit        ***
//*** https://github.com/AAChartModel/AAChartKit-Swift  ***
//***...................................................***
//*************** ...... SOURCE CODE ...... ***************

/*
 
 * -------------------------------------------------------------------------------
 *
 *  🌕 🌖 🌗 🌘  ❀❀❀   WARM TIPS!!!   ❀❀❀ 🌑 🌒 🌓 🌔
 *
 * Please contact me on GitHub,if there are any problems encountered in use.
 * GitHub Issues : https://github.com/AAChartModel/AAChartKit-Swift/issues
 * -------------------------------------------------------------------------------
 * And if you want to contribute for this project, please contact me as well
 * GitHub        : https://github.com/AAChartModel
 * StackOverflow : https://stackoverflow.com/users/12302132/codeforu
 * JianShu       : https://www.jianshu.com/u/f1e6753d4254
 * SegmentFault  : https://segmentfault.com/u/huanghunbieguan
 *
 * -------------------------------------------------------------------------------
 
 */

extension AAGradientColor {
    
    public class var oceanBlue: AAGradientColor {
        oceanBlueColor(.toTop)
    }
    
    public class var sanguine: AAGradientColor {
        sanguineColor(.toTop)
    }
    
    public class var lusciousLime: AAGradientColor {
        lusciousLimeColor(.toTop)
    }
    
    public class var purpleLake: AAGradientColor {
        purpleLakeColor(.toTop)
    }
    
    public class var freshPapaya: AAGradientColor {
        freshPapayaColor(.toTop)
    }
    
    public class var ultramarine: AAGradientColor {
        ultramarineColor(.toTop)
    }
    
    public class var pinkSugar: AAGradientColor {
        pinkSugarColor(.toTop)
    }
    
    public class var lemonDrizzle: AAGradientColor {
        lemonDrizzleColor(.toTop)
    }
    
    public class var victoriaPurple: AAGradientColor {
        victoriaPurpleColor(.toTop)
    }
    
    public class var springGreens: AAGradientColor {
        springGreensColor(.toTop)
    }
    
    public class var mysticMauve: AAGradientColor {
        mysticMauveColor(.toTop)
    }
    
    public class var reflexSilver: AAGradientColor {
        reflexSilverColor(.toTop)
    }
    
    public class var neonGlow: AAGradientColor {
        neonGlowColor(.toTop)
    }
    
    public class var berrySmoothie: AAGradientColor {
        berrySmoothieColor(.toTop)
    }
    
    public class var newLeaf: AAGradientColor {
        newLeafColor(.toTop)
    }
    
    public class var cottonCandy: AAGradientColor {
        cottonCandyColor(.toTop)
    }
    
    public class var pixieDust: AAGradientColor {
        pixieDustColor(.toTop)
    }
    
    public class var fizzyPeach: AAGradientColor {
        fizzyPeachColor(.toTop)
    }
    
    public class var sweetDream: AAGradientColor {
        sweetDreamColor(.toTop)
    }
    
    public class var firebrick: AAGradientColor {
        firebrickColor(.toTop)
    }
    
    public class var wroughtIron: AAGradientColor {
        wroughtIronColor(.toTop)
    }
    
    public class var deepSea: AAGradientColor {
        deepSeaColor(.toTop)
    }
    
    public class var coastalBreeze: AAGradientColor {
        coastalBreezeColor(.toTop)
    }
    
    public class var eveningDelight: AAGradientColor {
        eveningDelightColor(.toTop)
    }
    
    public class func oceanBlueColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#2E3192",
            endColor: "#1BFFFF"
        )
    }
    
    public class func sanguineColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#D4145A",
            endColor: "#FBB03B"
        )
    }
    
    public class func lusciousLimeColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#009245",
            endColor: "#FCEE21"
        )
    }
    
    public class func purpleLakeColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#662D8C",
            endColor: "#ED1E79"
        )
    }
    
    public class func freshPapayaColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#ED1C24",
            endColor: "#FCEE21"
        )
    }
    
    public class func ultramarineColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#00A8C5",
            endColor: "#FFFF7E"
        )
    }
    
    public class func pinkSugarColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#D74177",
            endColor: "#FFE98A"
        )
    }
    
    public class func lemonDrizzleColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#FB872B",
            endColor: "#D9E021"
        )
    }
    
    public class func victoriaPurpleColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#312A6C",
            endColor: "#852D91"
        )
    }
    
    public class func springGreensColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#009E00",
            endColor: "#FFFF96"
        )
    }
    
    public class func mysticMauveColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#B066FE",
            endColor: "#63E2FF"
        )
    }
    
    public class func reflexSilverColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#808080",
            endColor: "#E6E6E6"
        )
    }
    
    public class func neonGlowColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#00FFA1",
            endColor: "#00FFFF"
        )
    }
    
    public class func berrySmoothieColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#8E78FF",
            endColor: "#FC7D7B"
        )
    }
    
    public class func newLeafColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#00537E",
            endColor: "#3AA17E"
        )
    }
    
    public class func cottonCandyColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#FCA5F1",
            endColor: "#B5FFFF"
        )
    }
    
    public class func pixieDustColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#D585FF",
            endColor: "#00FFEE"
        )
    }
    
    public class func fizzyPeachColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#F24645",
            endColor: "#EBC08D"
        )
    }
    
    public class func sweetDreamColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#3A3897",
            endColor: "#A3A1FF"
        )
    }
    
    public class func firebrickColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#45145A",
            endColor: "#FF5300"
        )
    }
    
    public class func wroughtIronColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#333333",
            endColor: "#5A5454"
        )
    }
    
    public class func deepSeaColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#4F00BC",
            endColor: "#29ABE2"
        )
    }
    
    public class func coastalBreezeColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#00B7FF",
            endColor: "#FFFFC7"
        )
    }
    
    public class func eveningDelightColor(
        _ direction: AALinearGradientDirection
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            startColor: "#93278F",
            endColor: "#00A99D"
        )
    }
    
}
