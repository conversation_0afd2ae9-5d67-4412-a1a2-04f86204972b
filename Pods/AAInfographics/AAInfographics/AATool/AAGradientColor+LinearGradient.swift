//
//  AALinearGradientColor.swift
//  AAInfographics
//
//  Created by AnAn on 2024/2/4.
//  Copyright © 2024 An An. All rights reserved.
//*************** ...... SOURCE CODE ...... ***************
//***...................................................***
//*** https://github.com/AAChartModel/AAChartKit        ***
//*** https://github.com/AAChartModel/AAChartKit-Swift  ***
//***...................................................***
//*************** ...... SOURCE CODE ...... ***************

/*
 
 * -------------------------------------------------------------------------------
 *
 *  🌕 🌖 🌗 🌘  ❀❀❀   WARM TIPS!!!   ❀❀❀ 🌑 🌒 🌓 🌔
 *
 * Please contact me on GitHub,if there are any problems encountered in use.
 * GitHub Issues : https://github.com/AAChartModel/AAChartKit-Swift/issues
 * -------------------------------------------------------------------------------
 * And if you want to contribute for this project, please contact me as well
 * GitHub        : https://github.com/AAChartModel
 * StackOverflow : https://stackoverflow.com/users/12302132/codeforu
 * JianShu       : https://www.jianshu.com/u/f1e6753d4254
 * SegmentFault  : https://segmentfault.com/u/huanghunbieguan
 *
 * -------------------------------------------------------------------------------
 
 */


/// https://api.highcharts.com/class-reference/Highcharts.LinearGradientColorObject
///
/// - vertical  : ⇧ top,  ⇩ bottom
/// - horizontal: ⇦ left, ⇨ right
/**
 —————————————————————————————————————————
    V\H     | ⇦ left       | ⇨ right
 -----------|--------------|--------------
 ⇧ top      | ⇖ topLeft    | ⇗ topRight
 ⇩ bottom   | ⇙ bottomLeft | ⇘ bottomRight
 —————————————————————————————————————————
 */
public enum AALinearGradientDirection: Int {
    case toTop = 0       //⇧⇧⇧⇧⇧⇧
    case toBottom        //⇩⇩⇩⇩⇩⇩
    case toLeft          //⇦⇦⇦⇦⇦⇦
    case toRight         //⇨⇨⇨⇨⇨⇨
    
    case toTopLeft       //⇖⇖⇖⇖⇖⇖
    case toTopRight      //⇗⇗⇗⇗⇗⇗
    case toBottomLeft    //⇙⇙⇙⇙⇙⇙
    case toBottomRight   //⇘⇘⇘⇘⇘⇘
}

extension AAGradientColor {
    
    public class func linearGradient(
        startColor: String,
        endColor: String
    ) -> AAGradientColor {
        linearGradient(
            direction: .toTop,
            startColor: startColor,
            endColor: endColor
        )
    }
    
    public class func linearGradient(
        direction: AALinearGradientDirection,
        startColor: String,
        endColor: String
    ) -> AAGradientColor {
        linearGradient(
            direction: direction,
            stops: [
                [0, startColor],
                [1, endColor]
            ]
        )
    }
    
    public class func linearGradient(
        direction: AALinearGradientDirection,
        stops: [[Any]]
    ) -> AAGradientColor {
        AAGradientColor()
            .linearGradient(linearGradientDirectionObject(direction))
            .stops(stops)
    }
    
    /**
     (0,0) ----------- (1,0)
     |                   |
     |                   |
     |                   |
     |                   |
     |                   |
     (0,1) ----------- (1,1)
     */
    private class func linearGradientDirectionObject(
        _ direction: AALinearGradientDirection
    ) -> AALinearGradient {
        switch direction {
        case .toTop:         return AALinearGradient(x1: 0, y1: 1, x2: 0, y2: 0)
        case .toBottom:      return AALinearGradient(x1: 0, y1: 0, x2: 0, y2: 1)
        case .toLeft:        return AALinearGradient(x1: 1, y1: 0, x2: 0, y2: 0)
        case .toRight:       return AALinearGradient(x1: 0, y1: 0, x2: 1, y2: 0)
            
        case .toTopLeft:     return AALinearGradient(x1: 1, y1: 1, x2: 0, y2: 0)
        case .toTopRight:    return AALinearGradient(x1: 0, y1: 1, x2: 1, y2: 0)
        case .toBottomLeft:  return AALinearGradient(x1: 1, y1: 0, x2: 0, y2: 1)
        case .toBottomRight: return AALinearGradient(x1: 0, y1: 0, x2: 1, y2: 1)
        }
    }
}
