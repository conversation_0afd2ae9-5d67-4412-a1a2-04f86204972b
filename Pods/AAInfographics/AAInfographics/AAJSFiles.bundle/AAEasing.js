
function configureTheChartAnimationEasingType(easingType){switch(easingType){case 1:return function(pos){return Math.pow(pos,2)};case 2:return function(pos){return -(Math.pow((pos-1),2)-1)};case 3:return function(pos){if((pos/=0.5)<1){return 0.5*Math.pow(pos,2)}return -0.5*((pos-=2)*pos-2)};case 4:return function(pos){return Math.pow(pos,3)};case 5:return function(pos){return(Math.pow((pos-1),3)+1)};case 6:return function(pos){if((pos/=0.5)<1){return 0.5*Math.pow(pos,3)}return 0.5*(Math.pow((pos-2),3)+2)};case 7:return function(pos){return Math.pow(pos,4)};case 8:return function(pos){return -(Math.pow((pos-1),4)-1)};case 9:return function(pos){if((pos/=0.5)<1){return 0.5*Math.pow(pos,4)}return -0.5*((pos-=2)*Math.pow(pos,3)-2)};case 10:return function(pos){return Math.pow(pos,5)};case 11:return function(pos){return(Math.pow((pos-1),5)+1)};case 12:return function(pos){if((pos/=0.5)<1){return 0.5*Math.pow(pos,5)}return 0.5*(Math.pow((pos-2),5)+2)};case 13:return function(pos){return -Math.cos(pos*(Math.PI/2))+1};case 14:return function(pos){return Math.sin(pos*(Math.PI/2))};case 15:return function(pos){return(-0.5*(Math.cos(Math.PI*pos)-1))};case 16:return function(pos){return(pos===0)?0:Math.pow(2,10*(pos-1))};case 17:return function(pos){return(pos===1)?1:-Math.pow(2,-10*pos)+1};case 18:return function(pos){if(pos===0){return 0}if(pos===1){return 1}if((pos/=0.5)<1){return 0.5*Math.pow(2,10*(pos-1))}return 0.5*(-Math.pow(2,-10*--pos)+2)};case 19:return function(pos){return -(Math.sqrt(1-(pos*pos))-1)};case 20:return function(pos){return Math.sqrt(1-Math.pow((pos-1),2))};case 21:return function(pos){if((pos/=0.5)<1){return -0.5*(Math.sqrt(1-pos*pos)-1)}return 0.5*(Math.sqrt(1-(pos-=2)*pos)+1)};case 22:return function(pos){if((pos)<(1/2.75)){return(7.5625*pos*pos)}else{if(pos<(2/2.75)){return(7.5625*(pos-=(1.5/2.75))*pos+0.75)}else{if(pos<(2.5/2.75)){return(7.5625*(pos-=(2.25/2.75))*pos+0.9375)}else{return(7.5625*(pos-=(2.625/2.75))*pos+0.984375)}}}};case 23:return function(pos){var s=1.70158;return(pos)*pos*((s+1)*pos-s)};case 24:return function(pos){var s=1.70158;return(pos=pos-1)*pos*((s+1)*pos+s)+1};case 25:return function(pos){var s=1.70158;if((pos/=0.5)<1){return 0.5*(pos*pos*(((s*=(1.525))+1)*pos-s))}return 0.5*((pos-=2)*pos*(((s*=(1.525))+1)*pos+s)+2)};case 26:return function(pos){return -1*Math.pow(4,-8*pos)*Math.sin((pos*6-1)*(2*Math.PI)/2)+1};case 27:return function(pos){var s=1.70158;return((pos/=0.5)<1)?0.5*(pos*pos*(((s*=(1.525))+1)*pos-s)):0.5*((pos-=2)*pos*(((s*=(1.525))+1)*pos+s)+2)};case 28:return function(pos){var s=1.70158;return pos*pos*((s+1)*pos-s)};case 29:return function(pos){var s=1.70158;return(pos-=1)*pos*((s+1)*pos+s)+1};case 30:return function(pos){if(pos<(1/2.75)){return(7.5625*pos*pos)}else{if(pos<(2/2.75)){return(7.5625*(pos-=(1.5/2.75))*pos+0.75)}else{if(pos<(2.5/2.75)){return(7.5625*(pos-=(2.25/2.75))*pos+0.9375)}else{return(7.5625*(pos-=(2.625/2.75))*pos+0.984375)}}}};case 31:return function(pos){if(pos<(1/2.75)){return(7.5625*pos*pos)}else{if(pos<(2/2.75)){return 2-(7.5625*(pos-=(1.5/2.75))*pos+0.75)}else{if(pos<(2.5/2.75)){return 2-(7.5625*(pos-=(2.25/2.75))*pos+0.9375)}else{return 2-(7.5625*(pos-=(2.625/2.75))*pos+0.984375)}}}};case 32:return function(pos){if((pos/=0.5)<1){return 0.5*Math.pow(pos,4)}return -0.5*((pos-=2)*Math.pow(pos,3)-2)};case 33:return function(pos){return Math.pow(pos,4)};case 34:return function(pos){return Math.pow(pos,0.25)}}};
