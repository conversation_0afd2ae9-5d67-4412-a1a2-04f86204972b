<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=3.0,user-scalable = no">
            <script src="AAHighcharts.js"></script>
<!--            <script src="AAHighcharts-More.js"></script>-->
<!--            <script src="AAFunnel.js"></script>-->
            <script src="AAEasing.js"></script>
<!--            <script src="AARounded-Corners.js"></script>-->
            
<!--            <script src="https://code.highcharts.com/highcharts.js"></script>-->
<!--            <script src="https://code.highcharts.com/highcharts-more.js"></script>-->
<!--            <script src="https://code.highcharts.com/modules/funnel.js"></script>-->

            <style>
                * {-webkit-user-select: none;
                    user-select: none;
                }
                
                #container {
                    height: 100%;
                    width: 100%;
                    position: absolute;
                }
            </style>
    </head>
    <body style="margin:0 0 0 0;">
        <div id="container">
        </div>
        <script>
            
        let aaGlobalChart;
        function loadTheHighChartView(sender, receivedWidth, receivedHeight) {
            let container = document.getElementById('container');
            if (receivedWidth != 0) {
                container.style.width = receivedWidth;
            }
            if(receivedHeight != 0) {
                container.style.height = receivedHeight;
            }

            let aaOptions = JSON.parse(sender, function (key, value) {
                if (typeof (value) == 'string'
                    && value.indexOf('function') !== -1) {
                    return eval(value)
                }
                return value;
            });

            if (aaOptions.defaultOptions) {
                Highcharts.setOptions({
                    lang: aaOptions.defaultOptions
                });
            }
            
            if (aaOptions.xAxisArray) {
                aaOptions.xAxis = aaOptions.xAxisArray
            }
            if (aaOptions.yAxisArray) {
                aaOptions.yAxis = aaOptions.yAxisArray
            }
            
            if (aaOptions.plotOptions) {
                configurePlotOptions(aaOptions);
            }
                            
            aaGlobalChart = Highcharts.chart('container', aaOptions);
        }

        function configurePlotOptions(aaOptions) {
            let aaPlotOptions = aaOptions.plotOptions;
            let animation = aaPlotOptions.series.animation;
            if (animation) {
                let animationEasingType = animation.easing;
                animation.easing = configureTheChartAnimationEasingType(animationEasingType);
            }
            
            if (aaOptions.clickEventEnabled == true) {
                configureChartClickEvent(aaPlotOptions);
            }
            
            if (aaOptions.touchEventEnabled == true) {
                configureChartTouchEvent(aaPlotOptions);
            }
        }
        
        function configureEventMessageBody(selectedPoint) {
            return {
                name: selectedPoint.series.name,
                y: selectedPoint.y,
                x: selectedPoint.x,
                category: selectedPoint.category ? selectedPoint.category : selectedPoint.name,
                offset: {
                    plotX: selectedPoint.plotX,
                    plotY: selectedPoint.plotY
                },
                index: selectedPoint.index,
            };
        }
            
        function configureChartClickEvent(aaPlotOptions) {
            let clickEventFunc = function() {
                let message = configureEventMessageBody(this);
                window.webkit.messageHandlers.click.postMessage(message);
            };
            
            aaPlotOptions.series.point.events.click = clickEventFunc;
        }
        
        function configureChartTouchEvent(aaPlotOptions) {
            let mouseOverEventFunc = function() {
                let message = configureEventMessageBody(this);
                window.webkit.messageHandlers.mouseover.postMessage(message);
            };
            
            aaPlotOptions.series.point.events.mouseOver = mouseOverEventFunc;
        }
        
        function onlyRefreshTheChartDataWithSeries(receivedSeries, animation) {
            let receivedSeriesArr = JSON.parse(receivedSeries);
            let animationBool = (animation === "true");

            aaGlobalChart.update({
                    series: receivedSeriesArr
                },
                true, false, animationBool
            );
        }
        
        function updateChart(optionsStr, redraw) {
            let options = JSON.parse(optionsStr);
            aaGlobalChart.update(options,redraw);
        }
        
        function addPointToChartSeries(elementIndex, optionsStr, redraw, shift, animation) {
            let options = JSON.parse(optionsStr);
            let redrawBool = (redraw === "true");
            let shiftBool = (shift === "true");
            let animationBool = (animation === "true");
            
            let seriesElement = aaGlobalChart.series[elementIndex];
            seriesElement.addPoint(options, redrawBool, shiftBool, animationBool);
        }
        
        function redrawWithAnimation(animation) {
            let animationBool = (animation === "true");
            aaGlobalChart.redraw(animationBool);
        }
        
        function setTheChartViewContentWidth(receivedWidth) {
            let container = document.getElementById('container');
            container.style.width = receivedWidth;
            aaGlobalChart.reflow();
        }
        
        function setTheChartViewContentHeight(receivedHeight) {
            let container = document.getElementById('container');
            container.style.height = receivedHeight;
            aaGlobalChart.reflow();
        }
        
        function setChartSeriesHidden(hidden) {
            let seriesLength = aaGlobalChart.series.length;
            for (let i = 0; i < seriesLength; i++) {
                let seriesElement = aaGlobalChart.series[i];
                if (hidden === "true") {
                    seriesElement.hide();
                } else {
                    seriesElement.show();
                }
            }
        }
        
        function showTheSeriesElementContentWithIndex(elementIndex) {
            let seriesElement = aaGlobalChart.series[elementIndex];
            seriesElement.show();
        }
        
        function hideTheSeriesElementContentWithIndex(elementIndex) {
            let seriesElement = aaGlobalChart.series[elementIndex];
            seriesElement.hide();
        }
        
        function addElementToChartSeriesWithElement(elementStr) {
            let seriesElement = JSON.parse(elementStr);
            aaGlobalChart.addSeries(seriesElement);
        }
        
        function removeElementFromChartSeriesWithElementIndex(elementIndex) {
            let seriesElement = aaGlobalChart.series[elementIndex];
            if (seriesElement) {
                seriesElement.remove(true);
            }
        }
        
        function evaluateTheJavaScriptStringFunction(jsStringFunction) {
            eval(jsStringFunction);
        }
            
        </script>
    </body>
</html>
