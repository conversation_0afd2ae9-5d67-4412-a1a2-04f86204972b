//
//  IQKeyboardExtended.swift
//  https://github.com/hackiftekhar/IQKeyboardCore
//  Copyright (c) 2013-24 <PERSON><PERSON><PERSON>.
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

import UIKit

/// Wrapper for IQKeyboardManager compatible types. This type provides an extension point for
/// convenience methods in IQKeyboardManager.
@available(iOSApplicationExtension, unavailable)
public struct IQKeyboardExtension<Base: AnyObject> {
    public private(set) weak var base: Base?
    fileprivate init(_ base: Base) {
        self.base = base
    }
}

// swiftlint:disable identifier_name
/// Represents an object type that is compatible with IQKeyboardManager. You can use `iq` property to get a
/// value in the namespace of IQKeyboardManager.
@available(iOSApplicationExtension, unavailable)
public protocol IQKeyboardExtended {
    /// Type being extended.
    associatedtype Base: AnyObject

    /// Instance IQKeyboardManager extension point.
    @MainActor
    var iq: IQKeyboardExtension<Base> { get set }
}

// swiftlint:disable unused_setter_value
@available(iOSApplicationExtension, unavailable)
public extension IQKeyboardExtended where Self: UIView {

    /// Instance IQKeyboardManager extension point.
    @MainActor
    var iq: IQKeyboardExtension<Self> {
        get { IQKeyboardExtension(self) }
        set {}
    }
}
// swiftlint:enable unused_setter_value
// swiftlint:enable identifier_name

@available(iOSApplicationExtension, unavailable)
@MainActor
extension UIView: IQKeyboardExtended {}
