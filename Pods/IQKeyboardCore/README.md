# IQKeyboardCore

[![CI Status](https://img.shields.io/travis/hackiftekhar/IQKeyboardCore.svg?style=flat)](https://travis-ci.org/hackiftekhar/IQKeyboardCore)
[![Version](https://img.shields.io/cocoapods/v/IQKeyboardCore.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardCore)
[![License](https://img.shields.io/cocoapods/l/IQKeyboardCore.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardCore)
[![Platform](https://img.shields.io/cocoapods/p/IQKeyboardCore.svg?style=flat)](https://cocoapods.org/pods/IQKeyboardCore)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

IQKeyboardCore is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'IQKeyboardCore'
```

## Author

hackiftekhar, <EMAIL>

## License

IQKeyboardCore is available under the MIT license. See the LICENSE file for more info.
