// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		005BFA80B78D4801EC762BA4AC941E6E /* IQKeyboardNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = D71CE5A99E3F59D20BADE77C784925AF /* IQKeyboardNotification.swift */; };
		00D3E475D6ABEB1CE1677818427C3149 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		01216C042B056FFF0FC7EE32A002C38A /* IQKeyboardCore-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1700FC3D5E527026F59B2F1B50D6111C /* IQKeyboardCore-dummy.m */; };
		0388214619F3AF5F5A122A6C8CF657C0 /* IQKeyboardToolbarManager+Action.swift in Sources */ = {isa = PBXBuildFile; fileRef = 61BCB0B63E7C327B517E537EA06A2E8D /* IQKeyboardToolbarManager+Action.swift */; };
		05F4664FC4EF89925161A064AA6481BB /* IQKeyboardManager+Position.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06027039BC4148C2943618E6B574D1C8 /* IQKeyboardManager+Position.swift */; };
		064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00EA741E8AF045FAC8F528905942C848 /* ConstraintLayoutSupportDSL.swift */; };
		07C23039E4A4427150B28BD169CE5217 /* IQTextView+Placeholderable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5658F4EEFC55C9126531F11356D66B57 /* IQTextView+Placeholderable.swift */; };
		0895381789B493294369A187CF16097B /* AAGradientColor+RadialGradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD80D3ED1DFA6E1344D3D71A308E16F0 /* AAGradientColor+RadialGradient.swift */; };
		09D303C994021652DF841C463DBD1DC5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */ = {isa = PBXBuildFile; fileRef = 948DF3A5E94617F57EA42D7431440372 /* ConstraintPriority.swift */; };
		0B20CBEC9B7CCB06F1D02DA9F75BE4C3 /* IQTextInputViewInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7B9C2EBA0232D5E1D8BE46CA45D61B6 /* IQTextInputViewInfoModel.swift */; };
		0BFADBC53818322DE1A7510A5E182333 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		0C7C78E9CA8DC47092FAF58E9FCA2A13 /* AAPlotBandsElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB4395A480B0F64C5E4B74C24DC9AC51 /* AAPlotBandsElement.swift */; };
		0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3412B730DD07DEA9D2DDF4CBE885B6A0 /* ConstraintAttributes.swift */; };
		0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 830802554E19B262E801B1C9FE1C4FA0 /* ConstraintView+Extensions.swift */; };
		0ECDECBD58B97546E720720259A7BAD1 /* IQKeyboardToolbarManager-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = EA978FF06E69B1F4D1A86C3901F308F3 /* IQKeyboardToolbarManager-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0F2BF86610A292C81CEA284234E49055 /* AAChartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36F0A5CF9DB7D5D14D450F0F5080D856 /* AAChartView.swift */; };
		1083DD9C24614A83BD0A843008DF366C /* AAStates.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7874C760A87744CAA853988F782C073 /* AAStates.swift */; };
		108B158BEE9DEEB452691092AD527853 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 27927B9D81746C01AB4D9BFA30E3E42A /* PrivacyInfo.xcprivacy */; };
		1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24FE5E6336D23CF3687EB2B3BB263460 /* ConstraintDirectionalInsets.swift */; };
		135BB12399A87F6A2B0267A9A9921A4F /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		154230695B4E3D61B8D2BD02DAA2CE91 /* IQKeyboardManager+Appearance_Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5FFF3605EDE796242FA0658BF55D96DE /* IQKeyboardManager+Appearance_Deprecated.swift */; };
		199FC5AC213A54B9326D31C049D3D7FF /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		21F8C522C6567324FEFF24831E3F4FA4 /* AAChartView+API.swift in Sources */ = {isa = PBXBuildFile; fileRef = 366EC1DC5095012A267D9F0E76E79A8B /* AAChartView+API.swift */; };
		23E7FD4F69645294729EB3839D524607 /* AAGradientColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1585D1FBEB52FE85D1257A65B2C9CDBB /* AAGradientColor.swift */; };
		24637C51989929A62E5E75585544A14D /* IQKeyboardManager+Debug.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3864A403EE6597B043FFCDFE05044EFF /* IQKeyboardManager+Debug.swift */; };
		261F1743BA33D3065C9FD7D3D7D85BED /* AAButtonTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = A847AD3E460355E7A99BA34C9C405FEF /* AAButtonTheme.swift */; };
		28831A65A8FFE6D27BB19BD0BC031492 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = C1EF70B6DEEA933E42F8C6C925410749 /* PrivacyInfo.xcprivacy */; };
		28CC28DFC13346866DEB2EF10D7D52CF /* IQKeyboardToolbar-IQKeyboardToolbar in Resources */ = {isa = PBXBuildFile; fileRef = A16FD016E18EACCF6B23F219F2E236FE /* IQKeyboardToolbar-IQKeyboardToolbar */; };
		28CF7A42811BA9B634621316DCAFFCC5 /* AAPlotOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 678771A5BB17F9F8D6F4C9F6812B6490 /* AAPlotOptions.swift */; };
		2B067825708D13E7E455426389397D1A /* UIView+IQKeyboardManagerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 927E2BCC33602AEC64F803D9840B66B1 /* UIView+IQKeyboardManagerExtension.swift */; };
		2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0346AE155F9256D138EA5817E9EA42C1 /* ConstraintInsetTarget.swift */; };
		2C1207AB554753DD987E471DEA8E9DED /* AAAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6113FCF1F3674DDC72349B53F760118 /* AAAnimation.swift */; };
		2C8100B337EB10A4BE7207A038E0E619 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F14DE37254217067E8A295AF5FFB3B27 /* PrivacyInfo.xcprivacy */; };
		2CE3EDDFC3568BED3EBB743D36BD0A69 /* AAPie.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5D5A8D7371AFDE3C9318CA92BA195DA8 /* AAPie.swift */; };
		2D55B4DE112B7E622442F6BC03B6B0D1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 02BF6CF287EAC01539B309678BF5D047 /* PrivacyInfo.xcprivacy */; };
		2F4337F8364DE531ADD9281A9F7CBA9F /* AAPackedbubble.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51AB1DE11761F6B92B12BC648255364A /* AAPackedbubble.swift */; };
		2FE51C93DC6B9C0A99E451D138D0A1C8 /* IQKeyboardToolbarManager+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CCCF24B8A4AF21847D861E5C62F069A /* IQKeyboardToolbarManager+Internal.swift */; };
		30D6F928FC08B13B5BFEEB154A2089C1 /* IQKeyboardManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DDB6924DFF09E09ABCD1E7684B7CEF2 /* IQKeyboardManager.swift */; };
		312C7FD0CFDC0D70C789F4107C37D627 /* AALegend.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB9D1E420BCD08AA23D1F0BD71FC25D4 /* AALegend.swift */; };
		31B7EF92F0B2C64A627F77E3ECB2C36E /* IQPlaceholderable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 736F33B96A7601933D831FCCEF0C9759 /* IQPlaceholderable.swift */; };
		32FFA15324CC5CB9103D5AAE40D3BACE /* IQKeyboardReturnManager-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4882C6E7B2368B2E0DF8BBC737A360F0 /* IQKeyboardReturnManager-dummy.m */; };
		332EB3263B1002A5A0A146CCE70501AD /* IQRootControllerConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = DCB49FA9B1BCE2864D36EBD414A73750 /* IQRootControllerConfiguration.swift */; };
		353180A0F0DC4B61F2BDED206473D6F9 /* AADataLabels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24DA49E742F66E1502D74A15A24C0AF3 /* AADataLabels.swift */; };
		355A2A3962A82AE25D219DB774CB6AEE /* UIView+ResignObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6FB77CEB4BB69193BF43642804E943D /* UIView+ResignObjc.swift */; };
		356F2FE4EDACF39DAFA756E537ECD06B /* AAScatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 882CC6D760CEE94B78853BD7748DE669 /* AAScatter.swift */; };
		3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 169ADC49122B3EDC175B048540C3BEB5 /* ConstraintOffsetTarget.swift */; };
		378BD5280F6C07B11CF30B253D824972 /* IQKeyboardExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3668CBA336295C38ADE69ECCD143536E /* IQKeyboardExtended.swift */; };
		39B33D7873CD89C6A7E96ACD36A1EDFB /* UIScrollView+IQKeyboardManagerExtensionObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2308CD4FE177F6A0557DDA9F54AA2CB6 /* UIScrollView+IQKeyboardManagerExtensionObjc.swift */; };
		3A6B20BEE4F7A5B5835DE5D1E509754E /* IQActiveConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2CD4053154999DA33EBDDD68A141CBC2 /* IQActiveConfiguration.swift */; };
		3B396DC2A9F9041CE91A70FA4F9AF034 /* IQKeyboardReturnManager+UITextFieldDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 42CEB7C81D117C4CEF3E789414624D15 /* IQKeyboardReturnManager+UITextFieldDelegate.swift */; };
		3C2B8C97374497F718B2D7BCC3458F17 /* IQDeepResponderContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CEBB26D9965DCDA6B5AF350A85D78DE6 /* IQDeepResponderContainerView.swift */; };
		3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 633F22429340E42E3DD1FD0492BB608D /* ConstraintLayoutGuideDSL.swift */; };
		3E18C78B7EE7A67C5F0EA37729E426D5 /* IQBarButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = D72A89474D6BF0C32331C67C0B7BF330 /* IQBarButtonItem.swift */; };
		3E52BAFA9AECEF850B67B1E7C32650BF /* AACrosshair.swift in Sources */ = {isa = PBXBuildFile; fileRef = 40C9E3AFB92950D77968E8E145B7B9EA /* AACrosshair.swift */; };
		3E715D5BADDEACEEF30D6A4CA82CDA9B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		40BB4A23AD4A18E81678F58EAA90EB85 /* UIScrollView+IQKeyboardManagerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA835EA2C8E9C9738591E70E5BE312F7 /* UIScrollView+IQKeyboardManagerExtension.swift */; };
		40D4BCADC1B55A0ED76FB62CDA658A0E /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		41C06ED65F5CBD79E1972D5F1FC0430A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		424D364F1A2A84A922862D2B3BA97C29 /* UIView+RespondersObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C4FAE447F189859E32DFDAD42B065AB /* UIView+RespondersObjc.swift */; };
		425740165A03C6C08E35C39415112D02 /* IQTitleBarButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3EF2F873573308010090B3DF65B1856E /* IQTitleBarButtonItem.swift */; };
		45302C6ECBE939C4D1A647A03905D395 /* IQKeyboardToolbarManager+Debug.swift in Sources */ = {isa = PBXBuildFile; fileRef = E6E5753AAB71738B3DCF73B22EDA6330 /* IQKeyboardToolbarManager+Debug.swift */; };
		45459AF4B2FD64DD66FBA5889A811D9E /* AAInfographics-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = B9877EA17939ABB14328F469812E495F /* AAInfographics-dummy.m */; };
		47C9A000C20E04EC69BFEEF3C6AC6256 /* IQKeyboardAppearanceManager+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6998532A010666546D80F2DBDE3C86C /* IQKeyboardAppearanceManager+Internal.swift */; };
		48DF918490C82FE2849F2058784FCB79 /* AAPane.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953149E87E3D5AC6C1C7949C9A5F39E4 /* AAPane.swift */; };
		49777AA7557617FEF959EEF1C145CCE5 /* IQKeyboardToolbar-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 298606B078D7DDFD635F774C76564B2A /* IQKeyboardToolbar-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4A84D7C091F340B22AAD8B88C1522160 /* AASeries.swift in Sources */ = {isa = PBXBuildFile; fileRef = CAB90023495AC282248E0C52BCC096FE /* AASeries.swift */; };
		4A8C99371A914D0973745DA8124C217A /* IQKeyboardToolbarConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6568006FC2741928FF7F9B0F6D72BFFC /* IQKeyboardToolbarConfiguration.swift */; };
		4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33D14A15223B424596A94B0FF9AD0455 /* ConstraintMaker.swift */; };
		4FAF2E7DAF9B75AE20567FED3789F8E8 /* IQInvocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3AAB4E186B6B347149E82678971AAA /* IQInvocation.swift */; };
		******************************** /* AABubbleLegend.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* AABubbleLegend.swift */; };
		513FF454FF96C8751781104F7A651A9B /* AATooltip.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD29EA9920196D0F190AA0EEFC6D22C8 /* AATooltip.swift */; };
		568C235842D9191E16FFB6BCC2DBF6ED /* AASubtitle.swift in Sources */ = {isa = PBXBuildFile; fileRef = EFFB1E237E7530F28922CE86616BA63C /* AASubtitle.swift */; };
		57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C73AC6DE755A26665E1258A212D465CB /* SnapKit-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		585C44BD5E0588DC7A73859A35EAA957 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = FD27DFF0FCD29A77463AE8F829F1BA30 /* PrivacyInfo.xcprivacy */; };
		5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = C798641FDEEBA056B822BCE59F63D664 /* ConstraintItem.swift */; };
		59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */ = {isa = PBXBuildFile; fileRef = 928C5B1D5F6DFFE53DD010204B29B432 /* ConstraintLayoutGuide.swift */; };
		5C70A1A3F82408DA19F0E9CBB84D3023 /* Array+Sort.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B328B29F640CF228B946896DD4D7E80 /* Array+Sort.swift */; };
		5CEF2BF3A64BD87187DC43501A64A9DD /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 12BEBE9FE040DE3A5DC072CFA2995F93 /* PrivacyInfo.xcprivacy */; };
		5DF0C64A5C83FE297B37935E964A0615 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		61ED64340371AB30B936A019C22C376D /* UIView+IQKeyboardManagerExtensionObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36520879CF36902738D425E3699619CF /* UIView+IQKeyboardManagerExtensionObjc.swift */; };
		62B90C6FC83F91F91A4BF12022C7B050 /* UIView+Parent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4D799E2F764D99DE9D7FCF7E78A8343 /* UIView+Parent.swift */; };
		66B3CF0FA3383D7380C9020AF49B7BD3 /* IQTextView-IQTextView in Resources */ = {isa = PBXBuildFile; fileRef = 847044E56CBBCE1235A6F3CEF3F9F607 /* IQTextView-IQTextView */; };
		679F3BFC401ACA8F4D9A297B6238E9B1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		6CBE440BE36788B5E56E7C6124B2455A /* AALabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7A07A26886802B3DCEA9FF9EBE4C6AC /* AALabel.swift */; };
		6DE9FD143CBC24075601F8FE2ADC4115 /* IQKeyboardCore-IQKeyboardCore in Resources */ = {isa = PBXBuildFile; fileRef = 6E897F35E5E27028AC58B14B552FBC8D /* IQKeyboardCore-IQKeyboardCore */; };
		6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59E23ED8B58466857D8154939DD5CBA2 /* Constraint.swift */; };
		707F5D2C0DD51CE01C4BCD8ACAFDE4F4 /* AAChartViewPluginProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81976C190275C55728156A4578E85AA8 /* AAChartViewPluginProvider.swift */; };
		70A2FE54635EEBD101D5D79B01609C4D /* AAAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 470C24229DD4415925F70D20BB0524B2 /* AAAxis.swift */; };
		7359EA4AA9D0841330E2F010AD8F25A5 /* AAStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 94BC233410350C86C0CED0C676C1ADFF /* AAStyle.swift */; };
		769B4905AD35AD554AA9DE98DA8E4E11 /* IQKeyboardManager+Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 536FA061AB292A160328537ED19FC2EA /* IQKeyboardManager+Deprecated.swift */; };
		76E282B34C2415294759290B55080C73 /* IQTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D7F9F789A5CF99CB8D929DEC42BE5FE0 /* IQTextView.swift */; };
		7713C42CE43548B685917AA35DC638E9 /* ProjectBundlePathLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FC0B5A0F9C08EA7D45C929BCDBE3E6A /* ProjectBundlePathLoader.swift */; };
		77F1B40C21EFF28350EDA6A12263B098 /* IQKeyboardManager+Appearance.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CC8397D4F5724719D77DCE2AF54D2F0 /* IQKeyboardManager+Appearance.swift */; };
		794075641074D9F648585B37B851A44B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 92A4EE7422FF4ABA67FDA6B8C0E3042B /* PrivacyInfo.xcprivacy */; };
		7967D91375AB1477F543350DD4B93BD5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E437448F2FEF03EF2C18A3F8C0953EA /* ConstraintDescription.swift */; };
		7C7D4E1F438258C4363547D5CD7826CE /* AAMarker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 551814ECC746263DCC18EE3534515ADB /* AAMarker.swift */; };
		7CBA95E07A492F7311C7DD8EDF0B4172 /* IQScrollViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC43A481432B3B73A1EEA48C783F41D9 /* IQScrollViewConfiguration.swift */; };
		7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2312813D00F1457F9768DF3B98D3416 /* ConstraintViewDSL.swift */; };
		7F9F8BFE4A612F1EE0C0B1688185ED35 /* AAOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A387E0F0A567B9922E9BD07A28A7EDE0 /* AAOptions.swift */; };
		80E37F3FDB349D19B5DD572479F75315 /* AASeriesElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3482C9739866F370FDDB5D151C1C0EAA /* AASeriesElement.swift */; };
		85444D2EC8165B8F85D1BFF78B183F97 /* IQTextInputViewNotification-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F5ACEF7B197A86A54BB2C7D3B4124C78 /* IQTextInputViewNotification-dummy.m */; };
		858D141CDA8B7F21BCBE5CD8476F0F53 /* AAPlotLinesElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0303014C2F251F6F330E9ECAE5A4AC0E /* AAPlotLinesElement.swift */; };
		85E4FFD2D3E76E2F2A02B954F2D5C752 /* IQKeyboardNotification-IQKeyboardNotification in Resources */ = {isa = PBXBuildFile; fileRef = 120BD4C670EF00F4D5A40AA4B863A7AE /* IQKeyboardNotification-IQKeyboardNotification */; };
		868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C71D91972189FD2777485491A37C95E /* ConstraintDSL.swift */; };
		86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4493ED595B1208F5551ACE09D6AFCDD8 /* ConstraintView.swift */; };
		883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C9B0BF1A36337AF4F14010ADC211404 /* ConstraintMultiplierTarget.swift */; };
		8ACE9296AA479804EFD2D3CC417DE801 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08A8A6258AFFAC6569C0C9628AF06CCF /* ConstraintMakerRelatable+Extensions.swift */; };
		8C41EA954997897C3E30C5754010C05F /* UIViewController+ParentContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5726B4B5A35E9D5581EA681DBC3CAE57 /* UIViewController+ParentContainer.swift */; };
		8CBC72065F0261B373B958696985AF99 /* IQKeyboardResignHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F3916446A7F91567334620FD912480A /* IQKeyboardResignHandler.swift */; };
		8FBD02B3DA24907BCC9C2027BA177E73 /* IQKeyboardToolbarPlaceholderConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFAD35705E7E63E277B77F60601645AC /* IQKeyboardToolbarPlaceholderConfiguration.swift */; };
		90005505FB933A3047BE185F63A99FB3 /* AAChart.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE7EBFFFDC4C559E9E4F5353324A58A6 /* AAChart.swift */; };
		903685875CBD28FE7FBF09128948638E /* AAScrollablePlotArea.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A59A3AA7445C682D613C979654B7065 /* AAScrollablePlotArea.swift */; };
		91CA63A8C62222E89FBB8FA2B7FE9CDE /* IQKeyboardManager+Resign.swift in Sources */ = {isa = PBXBuildFile; fileRef = 348809270222C7DE52AB3C7E86D3B420 /* IQKeyboardManager+Resign.swift */; };
		923CA1FDC87DDEA88E02067BECD83C78 /* AABoxplot.swift in Sources */ = {isa = PBXBuildFile; fileRef = 861933080815BF37557CD12AE864B871 /* AABoxplot.swift */; };
		******************************** /* AAInfographics-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C303911F9CB8449FE5885F15AC28760C /* AAInfographics-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9298CB7C2E4AC4E751E535DA15BEEFA8 /* IQKeyboardManagerSwift-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 6B8A33E0B2D16B188C2D24EFD192CA38 /* IQKeyboardManagerSwift-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		946E96D8486BF7DAA6177B5D1E1C1655 /* IQKeyboardReturnManager-IQKeyboardReturnManager in Resources */ = {isa = PBXBuildFile; fileRef = 110BD425B6CAD6801539E2C6AB6E0662 /* IQKeyboardReturnManager-IQKeyboardReturnManager */; };
		9471927F8AAA87EA82DBEC2880E23A1D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		974E63AA1E0055F59B28DF981D0AC674 /* IQKeyboardToolbar-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = BD2855DAAA3DAAEAC38313F8F695C1C0 /* IQKeyboardToolbar-dummy.m */; };
		98F06146313DF71B8F040D7C9E81C3ED /* IQKeyboardNotification-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A63976351E9DFFA426D7F72A3C6A889C /* IQKeyboardNotification-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		990BE6EFC195B60FCFD4BE108238E6D0 /* AAChartModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 41DB99FBC10677CF5AE068365C6208B2 /* AAChartModel.swift */; };
		9938CF79581444ABC15D9B781D9D4434 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		9963434D8659BEAE9B61E4E9E2A5C412 /* AAYAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 601AB86DCBD6D93A0DDECA9D7A439CA6 /* AAYAxis.swift */; };
		99B2134E59C2BAFCFD0EAD4E508007A1 /* AAXAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD4EDDCA524B6008B1568202AD30FF55 /* AAXAxis.swift */; };
		9BBD05F55C106CC86C80DA2DA985C2B3 /* AASerializable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D9AFA483C8378D4BD45E6209E0E996A /* AASerializable.swift */; };
		9D22F88B7F1AF054C2BF025DFB767154 /* IQKeyboardManager+ToolbarManagerDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4F08E8B32D737961EE800C4BDF83DC /* IQKeyboardManager+ToolbarManagerDeprecated.swift */; };
		9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */ = {isa = PBXBuildFile; fileRef = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */; };
		9EA182CF7E8E54200088C50BA6156CD5 /* IQKeyboardReturnManager+UITextViewDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3D789E6F49AC836683A7EC86DF5AACBD /* IQKeyboardReturnManager+UITextViewDelegate.swift */; };
		A11DEF54C75555C0F7DA3230FBFA487B /* IQKeyboardReturnManager-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 56390A71C9082EEE2E7FBA0576CCA9E5 /* IQKeyboardReturnManager-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A19A0584AB4FB98199EED7AD862C56B1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		A335EBC0BF593E5DBC473E43CFB2199D /* IQTextView-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 990777C9FFBA09B86A10C7B3CCCC2613 /* IQTextView-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A39FE5F72261B202CC63722A7B357206 /* IQTextInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88584AC791F3FC51C93C5F6168E4BBCE /* IQTextInputView.swift */; };
		A5475FFD60FDD956994E00B1EC1A4EEB /* Pods-PanHeatLog-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3DBAA21826CE786A6FB19EC96209065A /* Pods-PanHeatLog-dummy.m */; };
		A6F1EFC3CDA009A4BE6CCBB01248DA21 /* UICollectionView+IndexPaths.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33CB043EE7B28223351E823564E71F0A /* UICollectionView+IndexPaths.swift */; };
		A7A915DE430C29728CF9C7730BC1FB26 /* IQTextInputViewNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06D3394507D9AE790950D1AFE5D6BBF7 /* IQTextInputViewNotification.swift */; };
		A95CCFD3E1129ADFA374308FC40B16CE /* IQKeyboardInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = A095F49DA3BD214B6164D1BC6F7FEF55 /* IQKeyboardInfo.swift */; };
		A983CCA5F6596F030EFCC1E27C1E766E /* UITableView+IndexPaths.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9F44BDBEB3196ED66C3B0A763045FFE /* UITableView+IndexPaths.swift */; };
		AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A637B309721E6ED448681E8B7E93C95 /* ConstraintMakerPrioritizable.swift */; };
		AB1CE4F61B10D7F11B3073AD4DDDEB8A /* IQBarButtonItemConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9A6468F7162CFDB1E2A915BC2A7AABC /* IQBarButtonItemConfiguration.swift */; };
		AB892B4247E11F91F6F76E2EF16ABFAE /* IQKeyboardToolbarManager-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 27C90F76847037B3D82100E2D1351F55 /* IQKeyboardToolbarManager-dummy.m */; };
		AC22AD1E1EF9CD9D6E242F3630A9A012 /* IQKeyboardManager+ToolbarManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = EF79E49BE9EFFD1F8B3AA290124F09E3 /* IQKeyboardManager+ToolbarManager.swift */; };
		ADE2069475CE82CF9D380EF4BB3FA06E /* IQKeyboardToolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09B54B4FA58970EC2E3729B35852BA4 /* IQKeyboardToolbar.swift */; };
		AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */ = {isa = PBXBuildFile; fileRef = EAA0306563071806EE0AC0EF961C1EC4 /* Debugging.swift */; };
		AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F74465262F2A5ED89D7019381AB2C65 /* ConstraintInsets.swift */; };
		B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 67E55C1AA82152BAB7A54C3382BA404D /* ConstraintLayoutSupport.swift */; };
		B11B3A15203508AEF294D78D656AD8DA /* AAColumn.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9DBFB859A6634802807074541EC83EFE /* AAColumn.swift */; };
		B2B121881248EA5DF4A53AA03A6F40DE /* AALayoutAlgorithm.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71972DFF76268A53C7DCDF45D36532B4 /* AALayoutAlgorithm.swift */; };
		B6ABA6005BF49ABF5DE72F15D625B088 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */; };
		B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2144EF52672DE8BA67D557BEF1E54916 /* ConstraintMakerFinalizable.swift */; };
		BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 16F5CA87085999D67D77C5E61D264F51 /* SnapKit-dummy.m */; };
		BBE9F66A30C6166BAB30367221072773 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = B01EDDED7A6980A3E7B53641AC217D3A /* PrivacyInfo.xcprivacy */; };
		BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36B1F6B1B79CA498A42C2ADC7A2CC1C5 /* ConstraintMakerRelatable.swift */; };
		BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 655F4FDC82F6C0710B486E06C945A46B /* LayoutConstraint.swift */; };
		C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5F77921D76830A02D5C519AFDF88F8DB /* ConstraintRelatableTarget.swift */; };
		C0AD493AE4A8DC8FF960D753D6D971E3 /* IQKeyboardManagerSwift-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = BE3639F9C36B30FDA074A867863270C9 /* IQKeyboardManagerSwift-dummy.m */; };
		C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8494813C615A022BA8CFE8233CBFA1EB /* ConstraintMakerEditable.swift */; };
		C20ECE96A97688090D5FE186DCF7B5D9 /* IQKeyboardManager+Resign_Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E0C87D0BAED94824B7D63B623525136 /* IQKeyboardManager+Resign_Deprecated.swift */; };
		C40219960F480E093C1155A69D951C2C /* AAJSFiles.bundle in Resources */ = {isa = PBXBuildFile; fileRef = ADB4D32C1D2BF103D081EA2D8427A11E /* AAJSFiles.bundle */; };
		C46472E5D2A254C2EA873CEE3E9607D6 /* AATitle.swift in Sources */ = {isa = PBXBuildFile; fileRef = EF707DF0E6E12D4132FA0D865A48A643 /* AATitle.swift */; };
		C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7DF026B4D66643B763EDED020D892B6 /* Typealiases.swift */; };
		C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A6D59042DEA36191C9C276ECC305E7C /* LayoutConstraintItem.swift */; };
		C7C6F1AD0C44C79C4A3416FCB32B0233 /* IQKeyboardResignHandler+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0CE8DA1F5D2E3376A4BB5C19BC8266CF /* IQKeyboardResignHandler+Internal.swift */; };
		C86A9CFD3486DDE59025E8E46C6421F5 /* IQTextInputViewNotification-IQTextInputViewNotification in Resources */ = {isa = PBXBuildFile; fileRef = F956E149F5B195BB7833F97FC6211AE8 /* IQTextInputViewNotification-IQTextInputViewNotification */; };
		C945CB4EC42271D435BF23E7749BE6BF /* IQKeyboardConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B9C7DBB05EA3F4068976B6D2EF9028A /* IQKeyboardConstants.swift */; };
		CCD99576883F2A882BE68EF947336D51 /* AAGradientColor+DefaultThemes.swift in Sources */ = {isa = PBXBuildFile; fileRef = A64AA798E17FD322725E67240F04EEB6 /* AAGradientColor+DefaultThemes.swift */; };
		CE45BBE9C0D24ADEC0F6985E0EFBF6E3 /* UIView+IQKeyboardExtensionDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1B979E784BD86C84518FDE83B0C8BBD /* UIView+IQKeyboardExtensionDeprecated.swift */; };
		CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = B8060CF2906C11EB335B31365487D03B /* ConstraintConstantTarget.swift */; };
		D170E4225C7FE88040733CF1737D3FC0 /* IQKeyboardNotification-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 508C2D5B5FEE438A581CAD8679555DA3 /* IQKeyboardNotification-dummy.m */; };
		D1B11F9CD7C549A01A38AC239F2DD42C /* IQKeyboardToolbarManager+Toolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 414A0CB2ACC9175FA918D494C76C0B38 /* IQKeyboardToolbarManager+Toolbar.swift */; };
		D416EE12D2B866AFE127EF06EFEEC820 /* UIView+IQKeyboardExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = D44098B400C6237BCB2DB9A1BAEA0156 /* UIView+IQKeyboardExtension.swift */; };
		D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC981B408A12DCABF5A8DB404D1CEAB6 /* ConstraintMakerExtendable.swift */; };
		D45A98C2962636D86E92AFD1273145E9 /* IQKeyboardToolbarManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 317384E501312754005FB3F94AFB2C41 /* IQKeyboardToolbarManager.swift */; };
		D6B15F2F110BC58F7635A9FD7A985C79 /* IQKeyboardManager+ActiveConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C550CE3F7553BF8B8E38E3228FAA7F1 /* IQKeyboardManager+ActiveConfiguration.swift */; };
		D6EFA9EAA562143F338EB164836B7837 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		D8486770C951AD52186C9D105E060A60 /* AAGradientColor+LinearGradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A00F4C6BAB2F3BC912F2BEC2B5FE5A7 /* AAGradientColor+LinearGradient.swift */; };
		D86B9BE31D45BF36BE78559FBA4254E6 /* AAColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = A38B25D93E79D04C3C9F7E28356E0F05 /* AAColor.swift */; };
		DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = EE86430300492B0CC41E0DE8C538F767 /* ConstraintPriorityTarget.swift */; };
		DF6B50CF0B84B2032987A351D1370D86 /* IQKeyboardToolbarManager-IQKeyboardToolbarManager in Resources */ = {isa = PBXBuildFile; fileRef = 80A40EDA3AF0499BDBCFF09467BB49AD /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */; };
		DF98F903BA88E29F2A6BB090AEE13E32 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = A8C7C46466E929C082B0F6F972B4F8D2 /* PrivacyInfo.xcprivacy */; };
		DFAC1DBC5816CA69B0D1450022F99099 /* IQKeyboardToolbarManager+Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FB1331981FB5E5CDE60213D88B66D8C /* IQKeyboardToolbarManager+Deprecated.swift */; };
		E076F42C12CB5D5A1F433625EC744CD8 /* IQKeyboardToolbarConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = B8D84DED77BA8B302659D07ACBBCA99E /* IQKeyboardToolbarConstants.swift */; };
		E1734E56DB48E6F238408C9A232C7051 /* UIView+Hierarchy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C03B4EB8E2D28BCD81EEDC51CEB9D0A /* UIView+Hierarchy.swift */; };
		E2597222CF62793A0EBAF1D5E86356D8 /* Combine.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 030EAF35766AF14A0C9409424A6C2B82 /* Combine.framework */; };
		E364BDD75ACDEF3302A44FC0D0459103 /* UIView+IQKeyboardExtensionObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00D46B9997092B2A102E596AC243131B /* UIView+IQKeyboardExtensionObjc.swift */; };
		E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 199DF50BC7E7EABE653BD7B71217F612 /* ConstraintDirectionalInsetTarget.swift */; };
		E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 668DAF8106B28CF265329E3EF262870E /* ConstraintLayoutGuide+Extensions.swift */; };
		E4A29F8C3ED424A7BC912DB796D03AFB /* UIView+ParentObjc.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6464E25338230129AFA9AC7855F81506 /* UIView+ParentObjc.swift */; };
		E6086BF7C4E1C88B0FCA145EB747AC08 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */; };
		E6FE2596512201193E95FC356C6E3351 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift in Resources */ = {isa = PBXBuildFile; fileRef = 8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */; };
		E84C30FA4A382D9C330E87FB32C009AE /* IQKeyboardAppearanceManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F44BC044C3EEE2BC22E6153A8A734FAA /* IQKeyboardAppearanceManager.swift */; };
		E862E5D575BED4D94CE1D4F24D841192 /* AAExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = EF06B9EE3EF62D8A49A2F0E4B36C2A91 /* AAExtension.swift */; };
		EBF34312A1F64FE81987440230E5BC49 /* IQKeyboardManager+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49C5925185C517D916C11E06F5A63A04 /* IQKeyboardManager+Internal.swift */; };
		ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D80896F510609A0DCD985AF468FEFA4 /* ConstraintRelation.swift */; };
		F115D688A0C7F668669F03A924EECED5 /* IQTextInputViewInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8222A3F28CC4ECA814C18BE20495A24F /* IQTextInputViewInfo.swift */; };
		F509F0508DF26AE4E831639DDC601832 /* IQTextView-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C84CEA3DF8361ECB28BB3DA7BFA70B02 /* IQTextView-dummy.m */; };
		F52AEF87130A1661D005DA57832273B7 /* IQKeyboardReturnManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A5F0FC2865E55C9041F0D159EDFBF65 /* IQKeyboardReturnManager.swift */; };
		F52F7402A43AC9A8D04DEA11E1B802C6 /* AALabels.swift in Sources */ = {isa = PBXBuildFile; fileRef = FFEE104B15D7F2572CB071FED5A2C7DB /* AALabels.swift */; };
		F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1DD76BFB2BF9C8EAE5F4B7C39F48830 /* UILayoutSupport+Extensions.swift */; };
		F70C920436626407BFB5139568A1DC51 /* UIView+Responders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 89E70FA496E300F2A84366E12EF3F0DB /* UIView+Responders.swift */; };
		F8959C17DDE29435F37374FF8D9FCCDB /* Pods-PanHeatLog-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = CB09FEEB59330C413D490AA0684E7121 /* Pods-PanHeatLog-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F98C041BD5C35C89C43DFBD4DF7EB712 /* AACredits.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88988F4DF531C402CA7880B249D1D4CE /* AACredits.swift */; };
		F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB2BE84611FD57296289A2176432D467 /* ConstraintConfig.swift */; };
		FBE2A348B6266D647CCEC706D32FE1A2 /* IQTextInputViewNotification-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 930663BF88BD59782FE618D46E4CA1DE /* IQTextInputViewNotification-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FD552610731EB624F8A47C3ACA927A15 /* UIView+Resign.swift in Sources */ = {isa = PBXBuildFile; fileRef = 657344CEDA69CAFB356264F9737560F3 /* UIView+Resign.swift */; };
		FE0D3260BF27D388B0200F1AC8151619 /* IQKeyboardCore-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = AC884F7670E71798C9D9E703C3EF3C9E /* IQKeyboardCore-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FF020D50D3B0742EFED9C172269D9D83 /* IQKeyboardAppearanceConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57FC420367F0BA1568022F39DE3509E2 /* IQKeyboardAppearanceConfiguration.swift */; };
		FF7980BF57A6EF580D9BF76FF90F683A /* Combine.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 030EAF35766AF14A0C9409424A6C2B82 /* Combine.framework */; };
		FF8555B8EC8BC27E2A2C22D5075BE646 /* AALang.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9F427AD6775657F912E14D3E518A2B6 /* AALang.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		14C5B64A34A875BE8CB3757966FB3433 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F9A1BF709B7BA4C24A83664EB1E1C7D4;
			remoteInfo = IQKeyboardCore;
		};
		1E9DC4658CA148739A46B8755068F72D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A26E6FD851C20D652B2755C1464A9990;
			remoteInfo = IQKeyboardNotification;
		};
		207D8392B352577B44CF2D60EA2781C0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C5613175BBC4BF67E36DB4FBEBC01D0;
			remoteInfo = "IQKeyboardToolbarManager-IQKeyboardToolbarManager";
		};
		2D30C1FED45F709B3F9DAA0BE1C7CC67 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F4FE17428FD0E607723A44F17231B7A1;
			remoteInfo = IQKeyboardToolbar;
		};
		3AF9157132618D4DBDE61849E76A4BC6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		420DE71FB542648339F5C040FE16DE8E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EEE261386011CDF271BE289F73FF5959;
			remoteInfo = "IQKeyboardCore-IQKeyboardCore";
		};
		46B62EF4643E8BAC81E143B61ECFD0F2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 020993F16DA5986DACE118349EBCE9E5;
			remoteInfo = IQKeyboardToolbarManager;
		};
		53E5E040779580711739A36432BA54F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 283C1F2EA88CD4413165801A6748A48E;
			remoteInfo = IQTextInputViewNotification;
		};
		58693826CB37223909682D6B7EFA4BF8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B247F77A0CD5E19C8187A9BA1EB58C09;
			remoteInfo = "IQKeyboardToolbar-IQKeyboardToolbar";
		};
		5BB96FA6BE13AA01EEDF69BC83B7920B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0981F89DB5DA3FFCFFEBDE1F56287054;
			remoteInfo = IQKeyboardReturnManager;
		};
		6C9149577EFE2637E2388CE4CABF6727 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B490E7485944099E16C9CBD79119D1D4;
			remoteInfo = IQKeyboardManagerSwift;
		};
		806A669B36A79529A5F494E1220F1E35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2B8FF445A5162845FAB9EC00FC92B694;
			remoteInfo = "IQKeyboardNotification-IQKeyboardNotification";
		};
		82A22D6A3149269639DDE19497D57F32 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 982A68D37F5DCBC1FC1FDC0BB2F0EB8E;
			remoteInfo = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
		};
		848D8D6D0161C281A7221C34C402F0BC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88810798DA63A2F6611B0970EA276DEC;
			remoteInfo = "IQKeyboardReturnManager-IQKeyboardReturnManager";
		};
		8562EFFE3415062B1AEAE8D2A4022ACC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F9A1BF709B7BA4C24A83664EB1E1C7D4;
			remoteInfo = IQKeyboardCore;
		};
		87CA28E7483E02B7F4B9DB460302DCAF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A26E6FD851C20D652B2755C1464A9990;
			remoteInfo = IQKeyboardNotification;
		};
		8CEDAB49EEB225938A1013164FA8A8A7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0981F89DB5DA3FFCFFEBDE1F56287054;
			remoteInfo = IQKeyboardReturnManager;
		};
		93A5F87A41307950FA6204EC7EA003C3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F9A1BF709B7BA4C24A83664EB1E1C7D4;
			remoteInfo = IQKeyboardCore;
		};
		94DB6F154A3F6AD33BDFC299B0718BFE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 020993F16DA5986DACE118349EBCE9E5;
			remoteInfo = IQKeyboardToolbarManager;
		};
		A25F8409D0E6A62A09234894DD4BFB09 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A6602BCAA6F4F932A586C41D0B7E019C;
			remoteInfo = "IQTextView-IQTextView";
		};
		A27358DBD154416D9730492A111858B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F4FE17428FD0E607723A44F17231B7A1;
			remoteInfo = IQKeyboardToolbar;
		};
		ACBA97825DAF02F206C1328DA2C798EC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 12890DE3ABBC2CA295E108358D85EE69;
			remoteInfo = IQTextView;
		};
		ADCD21E384E7BE7E4F7BFBD8D4892030 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F4FE17428FD0E607723A44F17231B7A1;
			remoteInfo = IQKeyboardToolbar;
		};
		B8F37B61BBAF70451995806FE5429DC4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 283C1F2EA88CD4413165801A6748A48E;
			remoteInfo = IQTextInputViewNotification;
		};
		C4D9378512695DF093901755B0C95CC6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F9A1BF709B7BA4C24A83664EB1E1C7D4;
			remoteInfo = IQKeyboardCore;
		};
		C72938D14938B642BD79003FC250F9CA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8A8DB685241263AFDF5E6B20FE67B93A;
			remoteInfo = "SnapKit-SnapKit_Privacy";
		};
		D1F1F8EC20024CB3947D3AA2D7FBA500 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4502C7427440BEB17A50C0BF6E638A85;
			remoteInfo = "IQTextInputViewNotification-IQTextInputViewNotification";
		};
		D328F8F9AA74F66F5C1157E758708DD8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 12890DE3ABBC2CA295E108358D85EE69;
			remoteInfo = IQTextView;
		};
		F42823EB4328B8AEE5EF5150AE308532 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 81AB6AA06BE8625CF139234A31B027FE;
			remoteInfo = AAInfographics;
		};
		FEF48ED8A7156196C864F8C49DFFD813 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 283C1F2EA88CD4413165801A6748A48E;
			remoteInfo = IQTextInputViewNotification;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00D46B9997092B2A102E596AC243131B /* UIView+IQKeyboardExtensionObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+IQKeyboardExtensionObjc.swift"; path = "IQKeyboardToolbar/Classes/IQKeyboardExtension/UIView+IQKeyboardExtensionObjc.swift"; sourceTree = "<group>"; };
		00EA741E8AF045FAC8F528905942C848 /* ConstraintLayoutSupportDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupportDSL.swift; path = Sources/ConstraintLayoutSupportDSL.swift; sourceTree = "<group>"; };
		01E069A0FED2D32BAF8408D14A052CF1 /* IQTextInputViewNotification.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQTextInputViewNotification.modulemap; sourceTree = "<group>"; };
		02BF6CF287EAC01539B309678BF5D047 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardCore/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		0303014C2F251F6F330E9ECAE5A4AC0E /* AAPlotLinesElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotLinesElement.swift; path = AAInfographics/AAOptionsModel/AAPlotLinesElement.swift; sourceTree = "<group>"; };
		030EAF35766AF14A0C9409424A6C2B82 /* Combine.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Combine.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Combine.framework; sourceTree = DEVELOPER_DIR; };
		0346AE155F9256D138EA5817E9EA42C1 /* ConstraintInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsetTarget.swift; path = Sources/ConstraintInsetTarget.swift; sourceTree = "<group>"; };
		06027039BC4148C2943618E6B574D1C8 /* IQKeyboardManager+Position.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Position.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Position.swift"; sourceTree = "<group>"; };
		06D3394507D9AE790950D1AFE5D6BBF7 /* IQTextInputViewNotification.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextInputViewNotification.swift; path = IQTextInputViewNotification/Classes/IQTextInputViewNotification.swift; sourceTree = "<group>"; };
		06E5B413FEA5A600D76636DA132F8FB9 /* IQTextInputViewNotification */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQTextInputViewNotification; path = IQTextInputViewNotification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		08A8A6258AFFAC6569C0C9628AF06CCF /* ConstraintMakerRelatable+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintMakerRelatable+Extensions.swift"; path = "Sources/ConstraintMakerRelatable+Extensions.swift"; sourceTree = "<group>"; };
		09ADB94D9386E3D4C698644E7E9F33F5 /* IQKeyboardReturnManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardReturnManager-Info.plist"; sourceTree = "<group>"; };
		0A00F4C6BAB2F3BC912F2BEC2B5FE5A7 /* AAGradientColor+LinearGradient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+LinearGradient.swift"; path = "AAInfographics/AATool/AAGradientColor+LinearGradient.swift"; sourceTree = "<group>"; };
		0CE8DA1F5D2E3376A4BB5C19BC8266CF /* IQKeyboardResignHandler+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardResignHandler+Internal.swift"; path = "IQKeyboardManagerSwift/Resign/IQKeyboardResignHandler+Internal.swift"; sourceTree = "<group>"; };
		0DAC4DD3569D55DC9D1B54A3FFB71721 /* IQKeyboardNotification-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardNotification-prefix.pch"; sourceTree = "<group>"; };
		0DFD4541FF9DAA31A2FC2A7F6D03ED22 /* IQTextView */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQTextView; path = IQTextView.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1013C65E0C9C59C8FD796EB3B1EFB2B2 /* IQKeyboardNotification.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardNotification.modulemap; sourceTree = "<group>"; };
		110BD425B6CAD6801539E2C6AB6E0662 /* IQKeyboardReturnManager-IQKeyboardReturnManager */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardReturnManager-IQKeyboardReturnManager"; path = IQKeyboardReturnManager.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		120BD4C670EF00F4D5A40AA4B863A7AE /* IQKeyboardNotification-IQKeyboardNotification */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardNotification-IQKeyboardNotification"; path = IQKeyboardNotification.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		12BEBE9FE040DE3A5DC072CFA2995F93 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardNotification/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		15848DEC7727A809830E7F6FC1B6CE0F /* IQKeyboardReturnManager */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardReturnManager; path = IQKeyboardReturnManager.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1585D1FBEB52FE85D1257A65B2C9CDBB /* AAGradientColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAGradientColor.swift; path = AAInfographics/AATool/AAGradientColor.swift; sourceTree = "<group>"; };
		169ADC49122B3EDC175B048540C3BEB5 /* ConstraintOffsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintOffsetTarget.swift; path = Sources/ConstraintOffsetTarget.swift; sourceTree = "<group>"; };
		16F5CA87085999D67D77C5E61D264F51 /* SnapKit-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SnapKit-dummy.m"; sourceTree = "<group>"; };
		1700FC3D5E527026F59B2F1B50D6111C /* IQKeyboardCore-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardCore-dummy.m"; sourceTree = "<group>"; };
		199DF50BC7E7EABE653BD7B71217F612 /* ConstraintDirectionalInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsetTarget.swift; path = Sources/ConstraintDirectionalInsetTarget.swift; sourceTree = "<group>"; };
		1B2DAA31000D78F8C0672BCE1F88EC38 /* IQKeyboardCore.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardCore.release.xcconfig; sourceTree = "<group>"; };
		1C03B4EB8E2D28BCD81EEDC51CEB9D0A /* UIView+Hierarchy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+Hierarchy.swift"; path = "IQKeyboardCore/Classes/UIKitExtensions/UIView+Hierarchy.swift"; sourceTree = "<group>"; };
		1D4C859E7799D93FC39352806DF3050C /* IQTextInputViewNotification.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQTextInputViewNotification.release.xcconfig; sourceTree = "<group>"; };
		2144EF52672DE8BA67D557BEF1E54916 /* ConstraintMakerFinalizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerFinalizable.swift; path = Sources/ConstraintMakerFinalizable.swift; sourceTree = "<group>"; };
		2308CD4FE177F6A0557DDA9F54AA2CB6 /* UIScrollView+IQKeyboardManagerExtensionObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIScrollView+IQKeyboardManagerExtensionObjc.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManagerExtension/UIScrollView+IQKeyboardManagerExtensionObjc.swift"; sourceTree = "<group>"; };
		24DA49E742F66E1502D74A15A24C0AF3 /* AADataLabels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AADataLabels.swift; path = AAInfographics/AAOptionsModel/AADataLabels.swift; sourceTree = "<group>"; };
		24FE5E6336D23CF3687EB2B3BB263460 /* ConstraintDirectionalInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsets.swift; path = Sources/ConstraintDirectionalInsets.swift; sourceTree = "<group>"; };
		255BF8D7047D07499B252CA15AB0E7EF /* IQTextInputViewNotification-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQTextInputViewNotification-Info.plist"; sourceTree = "<group>"; };
		2576B49CA74C10CC38367ADD2781AA30 /* ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist"; sourceTree = "<group>"; };
		25EB04025FF9AF24D2BA7911ED7F254A /* IQKeyboardManagerSwift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManagerSwift.debug.xcconfig; sourceTree = "<group>"; };
		27927B9D81746C01AB4D9BFA30E3E42A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardReturnManager/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		27C90F76847037B3D82100E2D1351F55 /* IQKeyboardToolbarManager-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardToolbarManager-dummy.m"; sourceTree = "<group>"; };
		2975E09F9DBBF8DD08F908360666EE53 /* IQTextView.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQTextView.modulemap; sourceTree = "<group>"; };
		298606B078D7DDFD635F774C76564B2A /* IQKeyboardToolbar-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardToolbar-umbrella.h"; sourceTree = "<group>"; };
		2B328B29F640CF228B946896DD4D7E80 /* Array+Sort.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Array+Sort.swift"; path = "IQKeyboardToolbarManager/Classes/UIKitExtensions/Array+Sort.swift"; sourceTree = "<group>"; };
		2B43D65C2303789C650E1D27E3FEF29D /* Pods-PanHeatLog-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-PanHeatLog-frameworks.sh"; sourceTree = "<group>"; };
		2B9C7DBB05EA3F4068976B6D2EF9028A /* IQKeyboardConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardConstants.swift; path = IQKeyboardCore/Classes/Constants/IQKeyboardConstants.swift; sourceTree = "<group>"; };
		2CD4053154999DA33EBDDD68A141CBC2 /* IQActiveConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQActiveConfiguration.swift; path = IQKeyboardManagerSwift/IQKeyboardManager/Configuration/IQActiveConfiguration.swift; sourceTree = "<group>"; };
		301336C32175C37C13E5734E0EEA1099 /* AAInfographics-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AAInfographics-Info.plist"; sourceTree = "<group>"; };
		309524066A641A50E7153B899E6389D6 /* AAInfographics-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AAInfographics-prefix.pch"; sourceTree = "<group>"; };
		3122AE7ACD0984D0DD5B88D292437588 /* IQKeyboardToolbar-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardToolbar-prefix.pch"; sourceTree = "<group>"; };
		317384E501312754005FB3F94AFB2C41 /* IQKeyboardToolbarManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardToolbarManager.swift; path = IQKeyboardToolbarManager/Classes/IQKeyboardToolbarManager.swift; sourceTree = "<group>"; };
		33CB043EE7B28223351E823564E71F0A /* UICollectionView+IndexPaths.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UICollectionView+IndexPaths.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/UIKitExtensions/UICollectionView+IndexPaths.swift"; sourceTree = "<group>"; };
		33D14A15223B424596A94B0FF9AD0455 /* ConstraintMaker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMaker.swift; path = Sources/ConstraintMaker.swift; sourceTree = "<group>"; };
		3412B730DD07DEA9D2DDF4CBE885B6A0 /* ConstraintAttributes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintAttributes.swift; path = Sources/ConstraintAttributes.swift; sourceTree = "<group>"; };
		3482C9739866F370FDDB5D151C1C0EAA /* AASeriesElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASeriesElement.swift; path = AAInfographics/AAChartCreator/AASeriesElement.swift; sourceTree = "<group>"; };
		348809270222C7DE52AB3C7E86D3B420 /* IQKeyboardManager+Resign.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Resign.swift"; path = "IQKeyboardManagerSwift/Resign/IQKeyboardManager+Resign.swift"; sourceTree = "<group>"; };
		355121649524E364DF1B7E1CFF2D972A /* IQKeyboardManagerSwift-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManagerSwift-prefix.pch"; sourceTree = "<group>"; };
		36520879CF36902738D425E3699619CF /* UIView+IQKeyboardManagerExtensionObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+IQKeyboardManagerExtensionObjc.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManagerExtension/UIView+IQKeyboardManagerExtensionObjc.swift"; sourceTree = "<group>"; };
		3668CBA336295C38ADE69ECCD143536E /* IQKeyboardExtended.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardExtended.swift; path = IQKeyboardCore/Classes/IQKeyboardExtended.swift; sourceTree = "<group>"; };
		366EC1DC5095012A267D9F0E76E79A8B /* AAChartView+API.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAChartView+API.swift"; path = "AAInfographics/AAChartCreator/AAChartView+API.swift"; sourceTree = "<group>"; };
		36B1F6B1B79CA498A42C2ADC7A2CC1C5 /* ConstraintMakerRelatable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerRelatable.swift; path = Sources/ConstraintMakerRelatable.swift; sourceTree = "<group>"; };
		36F0A5CF9DB7D5D14D450F0F5080D856 /* AAChartView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartView.swift; path = AAInfographics/AAChartCreator/AAChartView.swift; sourceTree = "<group>"; };
		3864A403EE6597B043FFCDFE05044EFF /* IQKeyboardManager+Debug.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Debug.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/Debug/IQKeyboardManager+Debug.swift"; sourceTree = "<group>"; };
		3C9B0BF1A36337AF4F14010ADC211404 /* ConstraintMultiplierTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMultiplierTarget.swift; path = Sources/ConstraintMultiplierTarget.swift; sourceTree = "<group>"; };
		3D789E6F49AC836683A7EC86DF5AACBD /* IQKeyboardReturnManager+UITextViewDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardReturnManager+UITextViewDelegate.swift"; path = "IQKeyboardReturnManager/Classes/Delegates/IQKeyboardReturnManager+UITextViewDelegate.swift"; sourceTree = "<group>"; };
		3DBAA21826CE786A6FB19EC96209065A /* Pods-PanHeatLog-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-PanHeatLog-dummy.m"; sourceTree = "<group>"; };
		3EF2F873573308010090B3DF65B1856E /* IQTitleBarButtonItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTitleBarButtonItem.swift; path = IQKeyboardToolbar/Classes/IQBarButtonItem/IQTitleBarButtonItem.swift; sourceTree = "<group>"; };
		3F6FFB4432E607EEA8C53ACCBCEBE58C /* IQKeyboardToolbarManager.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardToolbarManager.debug.xcconfig; sourceTree = "<group>"; };
		40C9E3AFB92950D77968E8E145B7B9EA /* AACrosshair.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AACrosshair.swift; path = AAInfographics/AAOptionsModel/AACrosshair.swift; sourceTree = "<group>"; };
		414A0CB2ACC9175FA918D494C76C0B38 /* IQKeyboardToolbarManager+Toolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardToolbarManager+Toolbar.swift"; path = "IQKeyboardToolbarManager/Classes/Toolbar/IQKeyboardToolbarManager+Toolbar.swift"; sourceTree = "<group>"; };
		41DB99FBC10677CF5AE068365C6208B2 /* AAChartModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartModel.swift; path = AAInfographics/AAChartCreator/AAChartModel.swift; sourceTree = "<group>"; };
		42CEB7C81D117C4CEF3E789414624D15 /* IQKeyboardReturnManager+UITextFieldDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardReturnManager+UITextFieldDelegate.swift"; path = "IQKeyboardReturnManager/Classes/Delegates/IQKeyboardReturnManager+UITextFieldDelegate.swift"; sourceTree = "<group>"; };
		4415F8C21967BB4909463B91C482BBD3 /* ResourceBundle-IQTextInputViewNotification-IQTextInputViewNotification-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQTextInputViewNotification-IQTextInputViewNotification-Info.plist"; sourceTree = "<group>"; };
		4493ED595B1208F5551ACE09D6AFCDD8 /* ConstraintView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintView.swift; path = Sources/ConstraintView.swift; sourceTree = "<group>"; };
		470C24229DD4415925F70D20BB0524B2 /* AAAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAAxis.swift; path = AAInfographics/AAOptionsModel/AAAxis.swift; sourceTree = "<group>"; };
		4882C6E7B2368B2E0DF8BBC737A360F0 /* IQKeyboardReturnManager-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardReturnManager-dummy.m"; sourceTree = "<group>"; };
		49C5925185C517D916C11E06F5A63A04 /* IQKeyboardManager+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Internal.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Internal.swift"; sourceTree = "<group>"; };
		4A6D59042DEA36191C9C276ECC305E7C /* LayoutConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraintItem.swift; path = Sources/LayoutConstraintItem.swift; sourceTree = "<group>"; };
		4C71D91972189FD2777485491A37C95E /* ConstraintDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDSL.swift; path = Sources/ConstraintDSL.swift; sourceTree = "<group>"; };
		4DDB6924DFF09E09ABCD1E7684B7CEF2 /* IQKeyboardManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardManager.swift; path = IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager.swift; sourceTree = "<group>"; };
		508C2D5B5FEE438A581CAD8679555DA3 /* IQKeyboardNotification-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardNotification-dummy.m"; sourceTree = "<group>"; };
		50DF48E3B8BC217CE8D63EFA9E6B66E2 /* IQKeyboardReturnManager.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardReturnManager.debug.xcconfig; sourceTree = "<group>"; };
		5125BBAA8DBB347FE5B8A5C95269F38C /* IQKeyboardCore.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardCore.debug.xcconfig; sourceTree = "<group>"; };
		5157ADB6CC75B2D9E888192341BC899D /* SnapKit.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SnapKit.modulemap; sourceTree = "<group>"; };
		51AB1DE11761F6B92B12BC648255364A /* AAPackedbubble.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPackedbubble.swift; path = AAInfographics/AAOptionsModel/AAPackedbubble.swift; sourceTree = "<group>"; };
		536FA061AB292A160328537ED19FC2EA /* IQKeyboardManager+Deprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Deprecated.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/Deprecated/IQKeyboardManager+Deprecated.swift"; sourceTree = "<group>"; };
		551814ECC746263DCC18EE3534515ADB /* AAMarker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAMarker.swift; path = AAInfographics/AAOptionsModel/AAMarker.swift; sourceTree = "<group>"; };
		56390A71C9082EEE2E7FBA0576CCA9E5 /* IQKeyboardReturnManager-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardReturnManager-umbrella.h"; sourceTree = "<group>"; };
		5658F4EEFC55C9126531F11356D66B57 /* IQTextView+Placeholderable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQTextView+Placeholderable.swift"; path = "IQTextView/Classes/IQTextView+Placeholderable.swift"; sourceTree = "<group>"; };
		5726B4B5A35E9D5581EA681DBC3CAE57 /* UIViewController+ParentContainer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIViewController+ParentContainer.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/UIKitExtensions/UIViewController+ParentContainer.swift"; sourceTree = "<group>"; };
		57FC420367F0BA1568022F39DE3509E2 /* IQKeyboardAppearanceConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardAppearanceConfiguration.swift; path = IQKeyboardManagerSwift/Appearance/IQKeyboardAppearanceConfiguration.swift; sourceTree = "<group>"; };
		59E23ED8B58466857D8154939DD5CBA2 /* Constraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Constraint.swift; path = Sources/Constraint.swift; sourceTree = "<group>"; };
		5C550CE3F7553BF8B8E38E3228FAA7F1 /* IQKeyboardManager+ActiveConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+ActiveConfiguration.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+ActiveConfiguration.swift"; sourceTree = "<group>"; };
		5D5A8D7371AFDE3C9318CA92BA195DA8 /* AAPie.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPie.swift; path = AAInfographics/AAOptionsModel/AAPie.swift; sourceTree = "<group>"; };
		5E0C87D0BAED94824B7D63B623525136 /* IQKeyboardManager+Resign_Deprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Resign_Deprecated.swift"; path = "IQKeyboardManagerSwift/Resign/IQKeyboardManager+Resign_Deprecated.swift"; sourceTree = "<group>"; };
		5F3F17006921F11C6592743383122554 /* SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SnapKit-Info.plist"; sourceTree = "<group>"; };
		5F77921D76830A02D5C519AFDF88F8DB /* ConstraintRelatableTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelatableTarget.swift; path = Sources/ConstraintRelatableTarget.swift; sourceTree = "<group>"; };
		5FFF3605EDE796242FA0658BF55D96DE /* IQKeyboardManager+Appearance_Deprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Appearance_Deprecated.swift"; path = "IQKeyboardManagerSwift/Appearance/IQKeyboardManager+Appearance_Deprecated.swift"; sourceTree = "<group>"; };
		601AB86DCBD6D93A0DDECA9D7A439CA6 /* AAYAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAYAxis.swift; path = AAInfographics/AAOptionsModel/AAYAxis.swift; sourceTree = "<group>"; };
		612C5E6191A78693DBD595DBB136A790 /* IQKeyboardToolbarManager.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardToolbarManager.release.xcconfig; sourceTree = "<group>"; };
		61BCB0B63E7C327B517E537EA06A2E8D /* IQKeyboardToolbarManager+Action.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardToolbarManager+Action.swift"; path = "IQKeyboardToolbarManager/Classes/Toolbar/IQKeyboardToolbarManager+Action.swift"; sourceTree = "<group>"; };
		633F22429340E42E3DD1FD0492BB608D /* ConstraintLayoutGuideDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuideDSL.swift; path = Sources/ConstraintLayoutGuideDSL.swift; sourceTree = "<group>"; };
		63BD9656198E55D4520A5B146AEAF3E8 /* ResourceBundle-IQTextView-IQTextView-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQTextView-IQTextView-Info.plist"; sourceTree = "<group>"; };
		6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		6464E25338230129AFA9AC7855F81506 /* UIView+ParentObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+ParentObjc.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/UIKitExtensions/UIView+ParentObjc.swift"; sourceTree = "<group>"; };
		647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		64A3274FD5C1E28B3DB0D6054718964A /* AAInfographics.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AAInfographics.release.xcconfig; sourceTree = "<group>"; };
		655F4FDC82F6C0710B486E06C945A46B /* LayoutConstraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraint.swift; path = Sources/LayoutConstraint.swift; sourceTree = "<group>"; };
		6568006FC2741928FF7F9B0F6D72BFFC /* IQKeyboardToolbarConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardToolbarConfiguration.swift; path = IQKeyboardToolbarManager/Classes/Configuration/IQKeyboardToolbarConfiguration.swift; sourceTree = "<group>"; };
		657344CEDA69CAFB356264F9737560F3 /* UIView+Resign.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+Resign.swift"; path = "IQKeyboardManagerSwift/Resign/UIKItExtensions/UIView+Resign.swift"; sourceTree = "<group>"; };
		668DAF8106B28CF265329E3EF262870E /* ConstraintLayoutGuide+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintLayoutGuide+Extensions.swift"; path = "Sources/ConstraintLayoutGuide+Extensions.swift"; sourceTree = "<group>"; };
		678771A5BB17F9F8D6F4C9F6812B6490 /* AAPlotOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotOptions.swift; path = AAInfographics/AAOptionsModel/AAPlotOptions.swift; sourceTree = "<group>"; };
		67E55C1AA82152BAB7A54C3382BA404D /* ConstraintLayoutSupport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupport.swift; path = Sources/ConstraintLayoutSupport.swift; sourceTree = "<group>"; };
		69203C33CD2CFF7682CD51E7B3FC9681 /* IQKeyboardManagerSwift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManagerSwift.release.xcconfig; sourceTree = "<group>"; };
		6A4F08E8B32D737961EE800C4BDF83DC /* IQKeyboardManager+ToolbarManagerDeprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+ToolbarManagerDeprecated.swift"; path = "IQKeyboardManagerSwift/IQKeyboardToolbarManager/IQKeyboardManager+ToolbarManagerDeprecated.swift"; sourceTree = "<group>"; };
		6A58ED3772A6B8BC1644BED01DE89051 /* IQKeyboardCore-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardCore-prefix.pch"; sourceTree = "<group>"; };
		6A5F0FC2865E55C9041F0D159EDFBF65 /* IQKeyboardReturnManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardReturnManager.swift; path = IQKeyboardReturnManager/Classes/IQKeyboardReturnManager.swift; sourceTree = "<group>"; };
		6A947966428D212C3F51EA82F8F54068 /* IQKeyboardToolbar.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardToolbar.modulemap; sourceTree = "<group>"; };
		6AFEF42C28D7198147A7DF00761D4817 /* IQTextView-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQTextView-prefix.pch"; sourceTree = "<group>"; };
		6B8A33E0B2D16B188C2D24EFD192CA38 /* IQKeyboardManagerSwift-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManagerSwift-umbrella.h"; sourceTree = "<group>"; };
		6CC8397D4F5724719D77DCE2AF54D2F0 /* IQKeyboardManager+Appearance.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Appearance.swift"; path = "IQKeyboardManagerSwift/Appearance/IQKeyboardManager+Appearance.swift"; sourceTree = "<group>"; };
		6E437448F2FEF03EF2C18A3F8C0953EA /* ConstraintDescription.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDescription.swift; path = Sources/ConstraintDescription.swift; sourceTree = "<group>"; };
		6E897F35E5E27028AC58B14B552FBC8D /* IQKeyboardCore-IQKeyboardCore */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardCore-IQKeyboardCore"; path = IQKeyboardCore.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		6F2A7C031B8CB97E1A421061C2A3F74B /* ResourceBundle-IQKeyboardNotification-IQKeyboardNotification-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardNotification-IQKeyboardNotification-Info.plist"; sourceTree = "<group>"; };
		6F3916446A7F91567334620FD912480A /* IQKeyboardResignHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardResignHandler.swift; path = IQKeyboardManagerSwift/Resign/IQKeyboardResignHandler.swift; sourceTree = "<group>"; };
		71972DFF76268A53C7DCDF45D36532B4 /* AALayoutAlgorithm.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALayoutAlgorithm.swift; path = AAInfographics/AAOptionsModel/AALayoutAlgorithm.swift; sourceTree = "<group>"; };
		7363301F8F78D0693B1A537144F5D9E1 /* Pods-PanHeatLog-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-PanHeatLog-acknowledgements.markdown"; sourceTree = "<group>"; };
		736B348BBD81DF75BD1B5F156A86EDC5 /* IQKeyboardNotification-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardNotification-Info.plist"; sourceTree = "<group>"; };
		736F33B96A7601933D831FCCEF0C9759 /* IQPlaceholderable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQPlaceholderable.swift; path = IQKeyboardToolbar/Placeholderable/IQPlaceholderable.swift; sourceTree = "<group>"; };
		74DFB4BCAAEE60F0E62E35E3C000585A /* IQKeyboardNotification.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardNotification.debug.xcconfig; sourceTree = "<group>"; };
		7BE69FE2D9A5CCA451444EF57551EF68 /* Pods-PanHeatLog-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PanHeatLog-acknowledgements.plist"; sourceTree = "<group>"; };
		7CC0E4A354F42DA837785C0F3C6CC1C1 /* IQKeyboardReturnManager.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardReturnManager.release.xcconfig; sourceTree = "<group>"; };
		7CCCF24B8A4AF21847D861E5C62F069A /* IQKeyboardToolbarManager+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardToolbarManager+Internal.swift"; path = "IQKeyboardToolbarManager/Classes/IQKeyboardToolbarManager+Internal.swift"; sourceTree = "<group>"; };
		7D51AF377E9A26C60377B28CB8CFB8E3 /* IQKeyboardCore-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardCore-Info.plist"; sourceTree = "<group>"; };
		7F74465262F2A5ED89D7019381AB2C65 /* ConstraintInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsets.swift; path = Sources/ConstraintInsets.swift; sourceTree = "<group>"; };
		7F7B660C82F68382BA2E6CDC1025E768 /* Pods-PanHeatLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PanHeatLog.debug.xcconfig"; sourceTree = "<group>"; };
		80A40EDA3AF0499BDBCFF09467BB49AD /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardToolbarManager-IQKeyboardToolbarManager"; path = IQKeyboardToolbarManager.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		81976C190275C55728156A4578E85AA8 /* AAChartViewPluginProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartViewPluginProvider.swift; path = AAInfographics/AAChartCreator/AAChartViewPluginProvider.swift; sourceTree = "<group>"; };
		8222A3F28CC4ECA814C18BE20495A24F /* IQTextInputViewInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextInputViewInfo.swift; path = IQTextInputViewNotification/Classes/IQTextInputViewInfo.swift; sourceTree = "<group>"; };
		82D7EBCCCDA4531DCA2056AD896E7DB0 /* IQKeyboardReturnManager-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardReturnManager-prefix.pch"; sourceTree = "<group>"; };
		830802554E19B262E801B1C9FE1C4FA0 /* ConstraintView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintView+Extensions.swift"; path = "Sources/ConstraintView+Extensions.swift"; sourceTree = "<group>"; };
		843572B8598B64E905321BD82DE53950 /* IQKeyboardReturnManager.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardReturnManager.modulemap; sourceTree = "<group>"; };
		847044E56CBBCE1235A6F3CEF3F9F607 /* IQTextView-IQTextView */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQTextView-IQTextView"; path = IQTextView.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		8494813C615A022BA8CFE8233CBFA1EB /* ConstraintMakerEditable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerEditable.swift; path = Sources/ConstraintMakerEditable.swift; sourceTree = "<group>"; };
		85146CF9F27897BBC9323631A61D3819 /* SnapKit-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-prefix.pch"; sourceTree = "<group>"; };
		861933080815BF37557CD12AE864B871 /* AABoxplot.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AABoxplot.swift; path = AAInfographics/AAOptionsModel/AABoxplot.swift; sourceTree = "<group>"; };
		882CC6D760CEE94B78853BD7748DE669 /* AAScatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAScatter.swift; path = AAInfographics/AAOptionsModel/AAScatter.swift; sourceTree = "<group>"; };
		88584AC791F3FC51C93C5F6168E4BBCE /* IQTextInputView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextInputView.swift; path = IQKeyboardCore/Classes/IQTextInputView.swift; sourceTree = "<group>"; };
		88988F4DF531C402CA7880B249D1D4CE /* AACredits.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AACredits.swift; path = AAInfographics/AAOptionsModel/AACredits.swift; sourceTree = "<group>"; };
		89E70FA496E300F2A84366E12EF3F0DB /* UIView+Responders.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+Responders.swift"; path = "IQKeyboardToolbarManager/Classes/UIKitExtensions/UIView+Responders.swift"; sourceTree = "<group>"; };
		8C4FAE447F189859E32DFDAD42B065AB /* UIView+RespondersObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+RespondersObjc.swift"; path = "IQKeyboardToolbarManager/Classes/UIKitExtensions/UIView+RespondersObjc.swift"; sourceTree = "<group>"; };
		8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift"; path = IQKeyboardManagerSwift.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		8D80896F510609A0DCD985AF468FEFA4 /* ConstraintRelation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelation.swift; path = Sources/ConstraintRelation.swift; sourceTree = "<group>"; };
		8D9AFA483C8378D4BD45E6209E0E996A /* AASerializable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASerializable.swift; path = AAInfographics/AAChartCreator/AASerializable.swift; sourceTree = "<group>"; };
		8E1512E6E12DE8CC25EB20F7A749477B /* IQTextView-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQTextView-Info.plist"; sourceTree = "<group>"; };
		8E3AAB4E186B6B347149E82678971AAA /* IQInvocation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQInvocation.swift; path = IQKeyboardToolbar/Classes/IQBarButtonItem/IQInvocation.swift; sourceTree = "<group>"; };
		8EF0B2C861CC00208F8093B66920E0CB /* IQTextView.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQTextView.release.xcconfig; sourceTree = "<group>"; };
		8FB1331981FB5E5CDE60213D88B66D8C /* IQKeyboardToolbarManager+Deprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardToolbarManager+Deprecated.swift"; path = "IQKeyboardToolbarManager/Classes/IQKeyboardToolbarManager+Deprecated.swift"; sourceTree = "<group>"; };
		8FC0B5A0F9C08EA7D45C929BCDBE3E6A /* ProjectBundlePathLoader.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProjectBundlePathLoader.swift; path = AAInfographics/ProjectBundlePathLoader.swift; sourceTree = "<group>"; };
		927E2BCC33602AEC64F803D9840B66B1 /* UIView+IQKeyboardManagerExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+IQKeyboardManagerExtension.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManagerExtension/UIView+IQKeyboardManagerExtension.swift"; sourceTree = "<group>"; };
		928C5B1D5F6DFFE53DD010204B29B432 /* ConstraintLayoutGuide.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuide.swift; path = Sources/ConstraintLayoutGuide.swift; sourceTree = "<group>"; };
		92A4EE7422FF4ABA67FDA6B8C0E3042B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardToolbarManager/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		92EF8B693A48AC488561735A70C3F318 /* IQKeyboardToolbarManager-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardToolbarManager-prefix.pch"; sourceTree = "<group>"; };
		930663BF88BD59782FE618D46E4CA1DE /* IQTextInputViewNotification-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQTextInputViewNotification-umbrella.h"; sourceTree = "<group>"; };
		948DF3A5E94617F57EA42D7431440372 /* ConstraintPriority.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriority.swift; path = Sources/ConstraintPriority.swift; sourceTree = "<group>"; };
		94BC233410350C86C0CED0C676C1ADFF /* AAStyle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAStyle.swift; path = AAInfographics/AAOptionsModel/AAStyle.swift; sourceTree = "<group>"; };
		953149E87E3D5AC6C1C7949C9A5F39E4 /* AAPane.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPane.swift; path = AAInfographics/AAOptionsModel/AAPane.swift; sourceTree = "<group>"; };
		9687765E0645223E6C71A362FDC12E69 /* Pods-PanHeatLog */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-PanHeatLog"; path = Pods_PanHeatLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		979486118B3E90C08386079D57962701 /* SnapKit */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SnapKit; path = SnapKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		990777C9FFBA09B86A10C7B3CCCC2613 /* IQTextView-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQTextView-umbrella.h"; sourceTree = "<group>"; };
		9A59A3AA7445C682D613C979654B7065 /* AAScrollablePlotArea.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAScrollablePlotArea.swift; path = AAInfographics/AAOptionsModel/AAScrollablePlotArea.swift; sourceTree = "<group>"; };
		9A637B309721E6ED448681E8B7E93C95 /* ConstraintMakerPrioritizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerPrioritizable.swift; path = Sources/ConstraintMakerPrioritizable.swift; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9DBFB859A6634802807074541EC83EFE /* AAColumn.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAColumn.swift; path = AAInfographics/AAOptionsModel/AAColumn.swift; sourceTree = "<group>"; };
		A095F49DA3BD214B6164D1BC6F7FEF55 /* IQKeyboardInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardInfo.swift; path = IQKeyboardNotification/Classes/IQKeyboardInfo.swift; sourceTree = "<group>"; };
		A09B54B4FA58970EC2E3729B35852BA4 /* IQKeyboardToolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardToolbar.swift; path = IQKeyboardToolbar/Classes/IQKeyboardToolbar.swift; sourceTree = "<group>"; };
		A16FD016E18EACCF6B23F219F2E236FE /* IQKeyboardToolbar-IQKeyboardToolbar */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardToolbar-IQKeyboardToolbar"; path = IQKeyboardToolbar.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		A387E0F0A567B9922E9BD07A28A7EDE0 /* AAOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAOptions.swift; path = AAInfographics/AAChartCreator/AAOptions.swift; sourceTree = "<group>"; };
		A38B25D93E79D04C3C9F7E28356E0F05 /* AAColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAColor.swift; path = AAInfographics/AATool/AAColor.swift; sourceTree = "<group>"; };
		A3ABED541C599386BF4D0680CBA5F957 /* IQKeyboardToolbarManager.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardToolbarManager.modulemap; sourceTree = "<group>"; };
		A4D799E2F764D99DE9D7FCF7E78A8343 /* UIView+Parent.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+Parent.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/UIKitExtensions/UIView+Parent.swift"; sourceTree = "<group>"; };
		A63976351E9DFFA426D7F72A3C6A889C /* IQKeyboardNotification-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardNotification-umbrella.h"; sourceTree = "<group>"; };
		A64AA798E17FD322725E67240F04EEB6 /* AAGradientColor+DefaultThemes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+DefaultThemes.swift"; path = "AAInfographics/AATool/AAGradientColor+DefaultThemes.swift"; sourceTree = "<group>"; };
		A6998532A010666546D80F2DBDE3C86C /* IQKeyboardAppearanceManager+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardAppearanceManager+Internal.swift"; path = "IQKeyboardManagerSwift/Appearance/IQKeyboardAppearanceManager+Internal.swift"; sourceTree = "<group>"; };
		A847AD3E460355E7A99BA34C9C405FEF /* AAButtonTheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAButtonTheme.swift; path = AAInfographics/AAOptionsModel/AAButtonTheme.swift; sourceTree = "<group>"; };
		A8C7C46466E929C082B0F6F972B4F8D2 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardToolbar/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardManagerSwift; path = IQKeyboardManagerSwift.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A9A6468F7162CFDB1E2A915BC2A7AABC /* IQBarButtonItemConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQBarButtonItemConfiguration.swift; path = IQKeyboardToolbar/Classes/IQBarButtonItem/IQBarButtonItemConfiguration.swift; sourceTree = "<group>"; };
		AC43A481432B3B73A1EEA48C783F41D9 /* IQScrollViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQScrollViewConfiguration.swift; path = IQKeyboardManagerSwift/IQKeyboardManager/Configuration/IQScrollViewConfiguration.swift; sourceTree = "<group>"; };
		AC884F7670E71798C9D9E703C3EF3C9E /* IQKeyboardCore-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardCore-umbrella.h"; sourceTree = "<group>"; };
		ADB4D32C1D2BF103D081EA2D8427A11E /* AAJSFiles.bundle */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = "wrapper.plug-in"; name = AAJSFiles.bundle; path = AAInfographics/AAJSFiles.bundle; sourceTree = "<group>"; };
		AE7EBFFFDC4C559E9E4F5353324A58A6 /* AAChart.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChart.swift; path = AAInfographics/AAOptionsModel/AAChart.swift; sourceTree = "<group>"; };
		AFAD35705E7E63E277B77F60601645AC /* IQKeyboardToolbarPlaceholderConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardToolbarPlaceholderConfiguration.swift; path = IQKeyboardToolbar/Classes/Placeholder/IQKeyboardToolbarPlaceholderConfiguration.swift; sourceTree = "<group>"; };
		B01EDDED7A6980A3E7B53641AC217D3A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQTextInputViewNotification/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		B15A7CEA361315CC78E49A788C43A09B /* SnapKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.release.xcconfig; sourceTree = "<group>"; };
		B365E882244C1431F3CC69B6C042CBDD /* IQTextInputViewNotification-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQTextInputViewNotification-prefix.pch"; sourceTree = "<group>"; };
		B4B1A58096F15199A14069C5D6AA1C99 /* IQKeyboardToolbar */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardToolbar; path = IQKeyboardToolbar.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B69B12F4F85CCE80420D350C3A0C2B42 /* Pods-PanHeatLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-PanHeatLog.release.xcconfig"; sourceTree = "<group>"; };
		B8060CF2906C11EB335B31365487D03B /* ConstraintConstantTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConstantTarget.swift; path = Sources/ConstraintConstantTarget.swift; sourceTree = "<group>"; };
		B8D84DED77BA8B302659D07ACBBCA99E /* IQKeyboardToolbarConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardToolbarConstants.swift; path = IQKeyboardToolbarManager/Classes/Constants/IQKeyboardToolbarConstants.swift; sourceTree = "<group>"; };
		B9877EA17939ABB14328F469812E495F /* AAInfographics-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AAInfographics-dummy.m"; sourceTree = "<group>"; };
		B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SnapKit-SnapKit_Privacy"; path = SnapKit_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B9F427AD6775657F912E14D3E518A2B6 /* AALang.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALang.swift; path = AAInfographics/AAOptionsModel/AALang.swift; sourceTree = "<group>"; };
		BC196AF111EDFA1F3DB902002D47B239 /* ResourceBundle-IQKeyboardToolbar-IQKeyboardToolbar-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardToolbar-IQKeyboardToolbar-Info.plist"; sourceTree = "<group>"; };
		BD2855DAAA3DAAEAC38313F8F695C1C0 /* IQKeyboardToolbar-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardToolbar-dummy.m"; sourceTree = "<group>"; };
		BE3639F9C36B30FDA074A867863270C9 /* IQKeyboardManagerSwift-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardManagerSwift-dummy.m"; sourceTree = "<group>"; };
		C103B42D6A9B4F8C0D22C6167B4B35A1 /* IQTextView.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQTextView.debug.xcconfig; sourceTree = "<group>"; };
		C1A405690A3BADC0EC66D4878DCBDE78 /* IQKeyboardToolbar.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardToolbar.debug.xcconfig; sourceTree = "<group>"; };
		C1B979E784BD86C84518FDE83B0C8BBD /* UIView+IQKeyboardExtensionDeprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+IQKeyboardExtensionDeprecated.swift"; path = "IQKeyboardToolbar/Classes/IQKeyboardExtension/UIView+IQKeyboardExtensionDeprecated.swift"; sourceTree = "<group>"; };
		C1EF70B6DEEA933E42F8C6C925410749 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Sources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		C303911F9CB8449FE5885F15AC28760C /* AAInfographics-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AAInfographics-umbrella.h"; sourceTree = "<group>"; };
		C73AC6DE755A26665E1258A212D465CB /* SnapKit-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-umbrella.h"; sourceTree = "<group>"; };
		C798641FDEEBA056B822BCE59F63D664 /* ConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintItem.swift; path = Sources/ConstraintItem.swift; sourceTree = "<group>"; };
		C7B9C2EBA0232D5E1D8BE46CA45D61B6 /* IQTextInputViewInfoModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextInputViewInfoModel.swift; path = IQKeyboardReturnManager/Classes/IQTextInputViewInfoModel.swift; sourceTree = "<group>"; };
		C84CEA3DF8361ECB28BB3DA7BFA70B02 /* IQTextView-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQTextView-dummy.m"; sourceTree = "<group>"; };
		C9F44BDBEB3196ED66C3B0A763045FFE /* UITableView+IndexPaths.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UITableView+IndexPaths.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/UIKitExtensions/UITableView+IndexPaths.swift"; sourceTree = "<group>"; };
		CA4A813BF5DB47478696D10E21404434 /* IQKeyboardNotification.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardNotification.release.xcconfig; sourceTree = "<group>"; };
		CAB90023495AC282248E0C52BCC096FE /* AASeries.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASeries.swift; path = AAInfographics/AAOptionsModel/AASeries.swift; sourceTree = "<group>"; };
		CB09FEEB59330C413D490AA0684E7121 /* Pods-PanHeatLog-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-PanHeatLog-umbrella.h"; sourceTree = "<group>"; };
		CB9D1E420BCD08AA23D1F0BD71FC25D4 /* AALegend.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALegend.swift; path = AAInfographics/AAOptionsModel/AALegend.swift; sourceTree = "<group>"; };
		CD80D3ED1DFA6E1344D3D71A308E16F0 /* AAGradientColor+RadialGradient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+RadialGradient.swift"; path = "AAInfographics/AATool/AAGradientColor+RadialGradient.swift"; sourceTree = "<group>"; };
		CD8CBB5E3EFF3A7A9113FB223545C4EC /* IQKeyboardManagerSwift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardManagerSwift-Info.plist"; sourceTree = "<group>"; };
		CEBB26D9965DCDA6B5AF350A85D78DE6 /* IQDeepResponderContainerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQDeepResponderContainerView.swift; path = IQKeyboardToolbarManager/Classes/IQDeepResponderContainerView.swift; sourceTree = "<group>"; };
		D07634E5CD15C4026C8D7B5AC5BE071A /* IQKeyboardToolbar.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardToolbar.release.xcconfig; sourceTree = "<group>"; };
		D29939705CA697DBB7BF226049895858 /* ResourceBundle-IQKeyboardToolbarManager-IQKeyboardToolbarManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardToolbarManager-IQKeyboardToolbarManager-Info.plist"; sourceTree = "<group>"; };
		D2AD956314FDCC72925D526682EA5060 /* IQKeyboardCore.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardCore.modulemap; sourceTree = "<group>"; };
		******************************** /* AABubbleLegend.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AABubbleLegend.swift; path = AAInfographics/AAOptionsModel/AABubbleLegend.swift; sourceTree = "<group>"; };
		D44098B400C6237BCB2DB9A1BAEA0156 /* UIView+IQKeyboardExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+IQKeyboardExtension.swift"; path = "IQKeyboardToolbar/Classes/IQKeyboardExtension/UIView+IQKeyboardExtension.swift"; sourceTree = "<group>"; };
		D4D4D009750A1AD25BD25A9526E010FE /* Pods-PanHeatLog.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-PanHeatLog.modulemap"; sourceTree = "<group>"; };
		D6113FCF1F3674DDC72349B53F760118 /* AAAnimation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAAnimation.swift; path = AAInfographics/AAOptionsModel/AAAnimation.swift; sourceTree = "<group>"; };
		D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AAInfographics; path = AAInfographics.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D6FB77CEB4BB69193BF43642804E943D /* UIView+ResignObjc.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+ResignObjc.swift"; path = "IQKeyboardManagerSwift/Resign/UIKItExtensions/UIView+ResignObjc.swift"; sourceTree = "<group>"; };
		D71CE5A99E3F59D20BADE77C784925AF /* IQKeyboardNotification.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardNotification.swift; path = IQKeyboardNotification/Classes/IQKeyboardNotification.swift; sourceTree = "<group>"; };
		D72A89474D6BF0C32331C67C0B7BF330 /* IQBarButtonItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQBarButtonItem.swift; path = IQKeyboardToolbar/Classes/IQBarButtonItem/IQBarButtonItem.swift; sourceTree = "<group>"; };
		D7F9F789A5CF99CB8D929DEC42BE5FE0 /* IQTextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextView.swift; path = IQTextView/Classes/IQTextView.swift; sourceTree = "<group>"; };
		D80AAFC640E43FBB9B64F9F605DFB992 /* ResourceBundle-IQKeyboardReturnManager-IQKeyboardReturnManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardReturnManager-IQKeyboardReturnManager-Info.plist"; sourceTree = "<group>"; };
		DA835EA2C8E9C9738591E70E5BE312F7 /* UIScrollView+IQKeyboardManagerExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIScrollView+IQKeyboardManagerExtension.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManagerExtension/UIScrollView+IQKeyboardManagerExtension.swift"; sourceTree = "<group>"; };
		DB1E296D1776DA3B5740A303C07BA352 /* AAInfographics.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AAInfographics.debug.xcconfig; sourceTree = "<group>"; };
		DCB49FA9B1BCE2864D36EBD414A73750 /* IQRootControllerConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQRootControllerConfiguration.swift; path = IQKeyboardManagerSwift/IQKeyboardManager/Configuration/IQRootControllerConfiguration.swift; sourceTree = "<group>"; };
		DD273551CCBCEA719C666DBA665DB8E3 /* AAInfographics.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AAInfographics.modulemap; sourceTree = "<group>"; };
		DD4EDDCA524B6008B1568202AD30FF55 /* AAXAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAXAxis.swift; path = AAInfographics/AAOptionsModel/AAXAxis.swift; sourceTree = "<group>"; };
		DE1F805FD7CE70CC220C2E16C7CB6812 /* ResourceBundle-IQKeyboardCore-IQKeyboardCore-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardCore-IQKeyboardCore-Info.plist"; sourceTree = "<group>"; };
		DE79F8FE13CCBBEF1969783C4AD6B554 /* IQKeyboardToolbarManager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardToolbarManager-Info.plist"; sourceTree = "<group>"; };
		E1DD76BFB2BF9C8EAE5F4B7C39F48830 /* UILayoutSupport+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UILayoutSupport+Extensions.swift"; path = "Sources/UILayoutSupport+Extensions.swift"; sourceTree = "<group>"; };
		E2312813D00F1457F9768DF3B98D3416 /* ConstraintViewDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintViewDSL.swift; path = Sources/ConstraintViewDSL.swift; sourceTree = "<group>"; };
		E43B1CFB668D2CE8D92609D9BCA179DC /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist"; sourceTree = "<group>"; };
		E6E5753AAB71738B3DCF73B22EDA6330 /* IQKeyboardToolbarManager+Debug.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardToolbarManager+Debug.swift"; path = "IQKeyboardToolbarManager/Classes/Debug/IQKeyboardToolbarManager+Debug.swift"; sourceTree = "<group>"; };
		E7A07A26886802B3DCEA9FF9EBE4C6AC /* AALabel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALabel.swift; path = AAInfographics/AAOptionsModel/AALabel.swift; sourceTree = "<group>"; };
		E957A48494763A0CB3608595235CA638 /* IQKeyboardManagerSwift.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardManagerSwift.modulemap; sourceTree = "<group>"; };
		EA978FF06E69B1F4D1A86C3901F308F3 /* IQKeyboardToolbarManager-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardToolbarManager-umbrella.h"; sourceTree = "<group>"; };
		EAA0306563071806EE0AC0EF961C1EC4 /* Debugging.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Debugging.swift; path = Sources/Debugging.swift; sourceTree = "<group>"; };
		EE86430300492B0CC41E0DE8C538F767 /* ConstraintPriorityTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriorityTarget.swift; path = Sources/ConstraintPriorityTarget.swift; sourceTree = "<group>"; };
		EF06B9EE3EF62D8A49A2F0E4B36C2A91 /* AAExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAExtension.swift; path = AAInfographics/AATool/AAExtension.swift; sourceTree = "<group>"; };
		EF707DF0E6E12D4132FA0D865A48A643 /* AATitle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AATitle.swift; path = AAInfographics/AAOptionsModel/AATitle.swift; sourceTree = "<group>"; };
		EF79E49BE9EFFD1F8B3AA290124F09E3 /* IQKeyboardManager+ToolbarManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+ToolbarManager.swift"; path = "IQKeyboardManagerSwift/IQKeyboardToolbarManager/IQKeyboardManager+ToolbarManager.swift"; sourceTree = "<group>"; };
		EFFB1E237E7530F28922CE86616BA63C /* AASubtitle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASubtitle.swift; path = AAInfographics/AAOptionsModel/AASubtitle.swift; sourceTree = "<group>"; };
		F0AE43462ADB1ABE08573711AAFCB412 /* Pods-PanHeatLog-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-PanHeatLog-Info.plist"; sourceTree = "<group>"; };
		F14DE37254217067E8A295AF5FFB3B27 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardManagerSwift/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		F2A725DAC8FDCE0DCD5231871B2F666E /* IQKeyboardToolbar-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardToolbar-Info.plist"; sourceTree = "<group>"; };
		F44BC044C3EEE2BC22E6153A8A734FAA /* IQKeyboardAppearanceManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardAppearanceManager.swift; path = IQKeyboardManagerSwift/Appearance/IQKeyboardAppearanceManager.swift; sourceTree = "<group>"; };
		F5ACEF7B197A86A54BB2C7D3B4124C78 /* IQTextInputViewNotification-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQTextInputViewNotification-dummy.m"; sourceTree = "<group>"; };
		F7874C760A87744CAA853988F782C073 /* AAStates.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAStates.swift; path = AAInfographics/AAOptionsModel/AAStates.swift; sourceTree = "<group>"; };
		F7DF026B4D66643B763EDED020D892B6 /* Typealiases.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Typealiases.swift; path = Sources/Typealiases.swift; sourceTree = "<group>"; };
		F9450613277EED51A0DE3A784EF4A0D3 /* IQTextInputViewNotification.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQTextInputViewNotification.debug.xcconfig; sourceTree = "<group>"; };
		F956E149F5B195BB7833F97FC6211AE8 /* IQTextInputViewNotification-IQTextInputViewNotification */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQTextInputViewNotification-IQTextInputViewNotification"; path = IQTextInputViewNotification.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		FA727BF5D9541C98C22C9ECC911576E2 /* IQKeyboardToolbarManager */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardToolbarManager; path = IQKeyboardToolbarManager.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FB2BE84611FD57296289A2176432D467 /* ConstraintConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConfig.swift; path = Sources/ConstraintConfig.swift; sourceTree = "<group>"; };
		FB4395A480B0F64C5E4B74C24DC9AC51 /* AAPlotBandsElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotBandsElement.swift; path = AAInfographics/AAOptionsModel/AAPlotBandsElement.swift; sourceTree = "<group>"; };
		FC7F0CF2EA5DF59C59D9995890DA5C47 /* IQKeyboardCore */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardCore; path = IQKeyboardCore.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FC981B408A12DCABF5A8DB404D1CEAB6 /* ConstraintMakerExtendable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerExtendable.swift; path = Sources/ConstraintMakerExtendable.swift; sourceTree = "<group>"; };
		FD27DFF0FCD29A77463AE8F829F1BA30 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQTextView/Assets/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		FD29EA9920196D0F190AA0EEFC6D22C8 /* AATooltip.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AATooltip.swift; path = AAInfographics/AAOptionsModel/AATooltip.swift; sourceTree = "<group>"; };
		FEF9AC89E9C4BD37AC0AB78B535D1CFC /* IQKeyboardNotification */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardNotification; path = IQKeyboardNotification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FFEC1C2CF15F9BCC07A92DE61FDBBE85 /* SnapKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.debug.xcconfig; sourceTree = "<group>"; };
		FFEE104B15D7F2572CB071FED5A2C7DB /* AALabels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALabels.swift; path = AAInfographics/AAOptionsModel/AALabels.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1ACABF250C77AFB5EC7A3D6284E4493C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		29017F23FB7F0B09B9D1E6052EC1AA9D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E0475FF91572EB671893970E3A33C31 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E2597222CF62793A0EBAF1D5E86356D8 /* Combine.framework in Frameworks */,
				41C06ED65F5CBD79E1972D5F1FC0430A /* Foundation.framework in Frameworks */,
				135BB12399A87F6A2B0267A9A9921A4F /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33428AC36668E3ED52DB70316F843FB8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		380C287193A2B18646EC79EEBAF08144 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4320A20732FD5CD14B63E2E51CC420DE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				679F3BFC401ACA8F4D9A297B6238E9B1 /* Foundation.framework in Frameworks */,
				E6086BF7C4E1C88B0FCA145EB747AC08 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4C94F3048E452D5CB9B37F4A099B5849 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B8C51C6BFA979E81A28179CCC2C0936 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				09D303C994021652DF841C463DBD1DC5 /* Foundation.framework in Frameworks */,
				8ACE9296AA479804EFD2D3CC417DE801 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7133E7E2A54488929356B41F5E5ABC1C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A19A0584AB4FB98199EED7AD862C56B1 /* Foundation.framework in Frameworks */,
				00D3E475D6ABEB1CE1677818427C3149 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79E123CE7C2BE00816E19CE19C99028D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9471927F8AAA87EA82DBEC2880E23A1D /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F76A8B49DA44DBD28915B6F9C9B2FE8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		84653E0AC28BE9DC3203CA0F84F23663 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8FE3BA34FC02FA47059B5BFF66372A82 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7967D91375AB1477F543350DD4B93BD5 /* Foundation.framework in Frameworks */,
				199FC5AC213A54B9326D31C049D3D7FF /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		925CB1889ABB4BCC5AD94E94C4BAE8C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5DF0C64A5C83FE297B37935E964A0615 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93193587BB52300D42937FD1B62BD7D7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FF7980BF57A6EF580D9BF76FF90F683A /* Combine.framework in Frameworks */,
				0BFADBC53818322DE1A7510A5E182333 /* Foundation.framework in Frameworks */,
				40D4BCADC1B55A0ED76FB62CDA658A0E /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A27C11FD69FE5449D243206A5A3F8BB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12BA8CA1D7E3FF2C044AB50A2259B6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B4742DD99303DB4F32189A0648880485 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B6ABA6005BF49ABF5DE72F15D625B088 /* Foundation.framework in Frameworks */,
				9938CF79581444ABC15D9B781D9D4434 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D07B79E83A18E3018EFA8CE654EF97CB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3E715D5BADDEACEEF30D6A4CA82CDA9B /* Foundation.framework in Frameworks */,
				D6EFA9EAA562143F338EB164836B7837 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E69AFA77E71336D6ED67A4FFDEFB9C14 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00A4DFFD407434EB55A5183E4FFC5D12 /* Resources */ = {
			isa = PBXGroup;
			children = (
				12BEBE9FE040DE3A5DC072CFA2995F93 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		03C5C200A0787E300053CFA8F53CA094 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7693C00AD1B9CA6AD0EFB3FCC3C6CAEC /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		09B1992CD0FDDF300DF544BFD6B64333 /* Appearance */ = {
			isa = PBXGroup;
			children = (
				57FC420367F0BA1568022F39DE3509E2 /* IQKeyboardAppearanceConfiguration.swift */,
				F44BC044C3EEE2BC22E6153A8A734FAA /* IQKeyboardAppearanceManager.swift */,
				A6998532A010666546D80F2DBDE3C86C /* IQKeyboardAppearanceManager+Internal.swift */,
				6CC8397D4F5724719D77DCE2AF54D2F0 /* IQKeyboardManager+Appearance.swift */,
				5FFF3605EDE796242FA0658BF55D96DE /* IQKeyboardManager+Appearance_Deprecated.swift */,
			);
			name = Appearance;
			sourceTree = "<group>";
		};
		0A19FB28F75A51712132A53CB2A0D801 /* Placeholderable */ = {
			isa = PBXGroup;
			children = (
				736F33B96A7601933D831FCCEF0C9759 /* IQPlaceholderable.swift */,
			);
			name = Placeholderable;
			sourceTree = "<group>";
		};
		0D24BAA7C7C4F335A62182ACC73E9131 /* IQKeyboardToolbarManager */ = {
			isa = PBXGroup;
			children = (
				2B328B29F640CF228B946896DD4D7E80 /* Array+Sort.swift */,
				CEBB26D9965DCDA6B5AF350A85D78DE6 /* IQDeepResponderContainerView.swift */,
				6568006FC2741928FF7F9B0F6D72BFFC /* IQKeyboardToolbarConfiguration.swift */,
				B8D84DED77BA8B302659D07ACBBCA99E /* IQKeyboardToolbarConstants.swift */,
				317384E501312754005FB3F94AFB2C41 /* IQKeyboardToolbarManager.swift */,
				61BCB0B63E7C327B517E537EA06A2E8D /* IQKeyboardToolbarManager+Action.swift */,
				E6E5753AAB71738B3DCF73B22EDA6330 /* IQKeyboardToolbarManager+Debug.swift */,
				8FB1331981FB5E5CDE60213D88B66D8C /* IQKeyboardToolbarManager+Deprecated.swift */,
				7CCCF24B8A4AF21847D861E5C62F069A /* IQKeyboardToolbarManager+Internal.swift */,
				414A0CB2ACC9175FA918D494C76C0B38 /* IQKeyboardToolbarManager+Toolbar.swift */,
				89E70FA496E300F2A84366E12EF3F0DB /* UIView+Responders.swift */,
				8C4FAE447F189859E32DFDAD42B065AB /* UIView+RespondersObjc.swift */,
				1333711579400A50DFB6186F79F43163 /* Resources */,
				B7ABEC042C252C916AD1C8F4EAA2F88E /* Support Files */,
			);
			name = IQKeyboardToolbarManager;
			path = IQKeyboardToolbarManager;
			sourceTree = "<group>";
		};
		0DCB1FB9A1831977DAD21022AC6334EA /* IQKeyboardToolbar */ = {
			isa = PBXGroup;
			children = (
				AB7CA5CCABDC874067DC7451EC386018 /* Core */,
				0A19FB28F75A51712132A53CB2A0D801 /* Placeholderable */,
				657BD73CA589AE4BEE4A74F65B12F9B5 /* Resources */,
				96FF4A25939679EAD19176C94E938B7B /* Support Files */,
			);
			name = IQKeyboardToolbar;
			path = IQKeyboardToolbar;
			sourceTree = "<group>";
		};
		1333711579400A50DFB6186F79F43163 /* Resources */ = {
			isa = PBXGroup;
			children = (
				92A4EE7422FF4ABA67FDA6B8C0E3042B /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		14C5274507F9F61E112A8255A04ED441 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B01EDDED7A6980A3E7B53641AC217D3A /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2AFC04584F02A53AE098AABA11F73F9B /* IQKeyboardToolbarManager */ = {
			isa = PBXGroup;
			children = (
				EF79E49BE9EFFD1F8B3AA290124F09E3 /* IQKeyboardManager+ToolbarManager.swift */,
				6A4F08E8B32D737961EE800C4BDF83DC /* IQKeyboardManager+ToolbarManagerDeprecated.swift */,
			);
			name = IQKeyboardToolbarManager;
			sourceTree = "<group>";
		};
		3A09BC7131DB907D7BA91926A65FE574 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				DD273551CCBCEA719C666DBA665DB8E3 /* AAInfographics.modulemap */,
				B9877EA17939ABB14328F469812E495F /* AAInfographics-dummy.m */,
				301336C32175C37C13E5734E0EEA1099 /* AAInfographics-Info.plist */,
				309524066A641A50E7153B899E6389D6 /* AAInfographics-prefix.pch */,
				C303911F9CB8449FE5885F15AC28760C /* AAInfographics-umbrella.h */,
				DB1E296D1776DA3B5740A303C07BA352 /* AAInfographics.debug.xcconfig */,
				64A3274FD5C1E28B3DB0D6054718964A /* AAInfographics.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AAInfographics";
			sourceTree = "<group>";
		};
		4081FFFBDD1B15DD33F699D976C4E967 /* IQKeyboardNotification */ = {
			isa = PBXGroup;
			children = (
				A095F49DA3BD214B6164D1BC6F7FEF55 /* IQKeyboardInfo.swift */,
				D71CE5A99E3F59D20BADE77C784925AF /* IQKeyboardNotification.swift */,
				00A4DFFD407434EB55A5183E4FFC5D12 /* Resources */,
				A713029B1951094E5FBF833FECB37522 /* Support Files */,
			);
			name = IQKeyboardNotification;
			path = IQKeyboardNotification;
			sourceTree = "<group>";
		};
		4E43B7C50617785F120D66B80611B3D5 /* Resources */ = {
			isa = PBXGroup;
			children = (
				F14DE37254217067E8A295AF5FFB3B27 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		4FF5664B823FA0F5F0E6C29CE05AE45A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E957A48494763A0CB3608595235CA638 /* IQKeyboardManagerSwift.modulemap */,
				BE3639F9C36B30FDA074A867863270C9 /* IQKeyboardManagerSwift-dummy.m */,
				CD8CBB5E3EFF3A7A9113FB223545C4EC /* IQKeyboardManagerSwift-Info.plist */,
				355121649524E364DF1B7E1CFF2D972A /* IQKeyboardManagerSwift-prefix.pch */,
				6B8A33E0B2D16B188C2D24EFD192CA38 /* IQKeyboardManagerSwift-umbrella.h */,
				25EB04025FF9AF24D2BA7911ED7F254A /* IQKeyboardManagerSwift.debug.xcconfig */,
				69203C33CD2CFF7682CD51E7B3FC9681 /* IQKeyboardManagerSwift.release.xcconfig */,
				2576B49CA74C10CC38367ADD2781AA30 /* ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardManagerSwift";
			sourceTree = "<group>";
		};
		511738346C4ADE89848CAF457B53DFC8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				2975E09F9DBBF8DD08F908360666EE53 /* IQTextView.modulemap */,
				C84CEA3DF8361ECB28BB3DA7BFA70B02 /* IQTextView-dummy.m */,
				8E1512E6E12DE8CC25EB20F7A749477B /* IQTextView-Info.plist */,
				6AFEF42C28D7198147A7DF00761D4817 /* IQTextView-prefix.pch */,
				990777C9FFBA09B86A10C7B3CCCC2613 /* IQTextView-umbrella.h */,
				C103B42D6A9B4F8C0D22C6167B4B35A1 /* IQTextView.debug.xcconfig */,
				8EF0B2C861CC00208F8093B66920E0CB /* IQTextView.release.xcconfig */,
				63BD9656198E55D4520A5B146AEAF3E8 /* ResourceBundle-IQTextView-IQTextView-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQTextView";
			sourceTree = "<group>";
		};
		610FDC795BB900F1DF176373A21CDB5D /* Resources */ = {
			isa = PBXGroup;
			children = (
				FD27DFF0FCD29A77463AE8F829F1BA30 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		657BD73CA589AE4BEE4A74F65B12F9B5 /* Resources */ = {
			isa = PBXGroup;
			children = (
				A8C7C46466E929C082B0F6F972B4F8D2 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		72BA2519E40011ECD9557813468E874D /* Resources */ = {
			isa = PBXGroup;
			children = (
				27927B9D81746C01AB4D9BFA30E3E42A /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		7693C00AD1B9CA6AD0EFB3FCC3C6CAEC /* iOS */ = {
			isa = PBXGroup;
			children = (
				030EAF35766AF14A0C9409424A6C2B82 /* Combine.framework */,
				6464447070DB8A1E9C56338928A9DC0F /* Foundation.framework */,
				647AECC2271497CFBB36311165FC0BF0 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		798CC9C0B5C713DE53587C078DAEE453 /* IQKeyboardManagerSwift */ = {
			isa = PBXGroup;
			children = (
				09B1992CD0FDDF300DF544BFD6B64333 /* Appearance */,
				DAFC6F3C9374760DC1B7FD4F59D0D3D1 /* Core */,
				2AFC04584F02A53AE098AABA11F73F9B /* IQKeyboardToolbarManager */,
				8C2FF633D928EB6855B56FA5EA8945AD /* Resign */,
				4E43B7C50617785F120D66B80611B3D5 /* Resources */,
				4FF5664B823FA0F5F0E6C29CE05AE45A /* Support Files */,
			);
			name = IQKeyboardManagerSwift;
			path = IQKeyboardManagerSwift;
			sourceTree = "<group>";
		};
		7B37F2F38B240BA3D3A6B518E3489CDF /* Resources */ = {
			isa = PBXGroup;
			children = (
				ADB4D32C1D2BF103D081EA2D8427A11E /* AAJSFiles.bundle */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		7D566B63DFD80BF10F9E3FA7D255B360 /* AAInfographics */ = {
			isa = PBXGroup;
			children = (
				D6113FCF1F3674DDC72349B53F760118 /* AAAnimation.swift */,
				470C24229DD4415925F70D20BB0524B2 /* AAAxis.swift */,
				861933080815BF37557CD12AE864B871 /* AABoxplot.swift */,
				******************************** /* AABubbleLegend.swift */,
				A847AD3E460355E7A99BA34C9C405FEF /* AAButtonTheme.swift */,
				AE7EBFFFDC4C559E9E4F5353324A58A6 /* AAChart.swift */,
				41DB99FBC10677CF5AE068365C6208B2 /* AAChartModel.swift */,
				36F0A5CF9DB7D5D14D450F0F5080D856 /* AAChartView.swift */,
				366EC1DC5095012A267D9F0E76E79A8B /* AAChartView+API.swift */,
				81976C190275C55728156A4578E85AA8 /* AAChartViewPluginProvider.swift */,
				A38B25D93E79D04C3C9F7E28356E0F05 /* AAColor.swift */,
				9DBFB859A6634802807074541EC83EFE /* AAColumn.swift */,
				88988F4DF531C402CA7880B249D1D4CE /* AACredits.swift */,
				40C9E3AFB92950D77968E8E145B7B9EA /* AACrosshair.swift */,
				24DA49E742F66E1502D74A15A24C0AF3 /* AADataLabels.swift */,
				EF06B9EE3EF62D8A49A2F0E4B36C2A91 /* AAExtension.swift */,
				1585D1FBEB52FE85D1257A65B2C9CDBB /* AAGradientColor.swift */,
				A64AA798E17FD322725E67240F04EEB6 /* AAGradientColor+DefaultThemes.swift */,
				0A00F4C6BAB2F3BC912F2BEC2B5FE5A7 /* AAGradientColor+LinearGradient.swift */,
				CD80D3ED1DFA6E1344D3D71A308E16F0 /* AAGradientColor+RadialGradient.swift */,
				E7A07A26886802B3DCEA9FF9EBE4C6AC /* AALabel.swift */,
				FFEE104B15D7F2572CB071FED5A2C7DB /* AALabels.swift */,
				B9F427AD6775657F912E14D3E518A2B6 /* AALang.swift */,
				71972DFF76268A53C7DCDF45D36532B4 /* AALayoutAlgorithm.swift */,
				CB9D1E420BCD08AA23D1F0BD71FC25D4 /* AALegend.swift */,
				551814ECC746263DCC18EE3534515ADB /* AAMarker.swift */,
				A387E0F0A567B9922E9BD07A28A7EDE0 /* AAOptions.swift */,
				51AB1DE11761F6B92B12BC648255364A /* AAPackedbubble.swift */,
				953149E87E3D5AC6C1C7949C9A5F39E4 /* AAPane.swift */,
				5D5A8D7371AFDE3C9318CA92BA195DA8 /* AAPie.swift */,
				FB4395A480B0F64C5E4B74C24DC9AC51 /* AAPlotBandsElement.swift */,
				0303014C2F251F6F330E9ECAE5A4AC0E /* AAPlotLinesElement.swift */,
				678771A5BB17F9F8D6F4C9F6812B6490 /* AAPlotOptions.swift */,
				882CC6D760CEE94B78853BD7748DE669 /* AAScatter.swift */,
				9A59A3AA7445C682D613C979654B7065 /* AAScrollablePlotArea.swift */,
				8D9AFA483C8378D4BD45E6209E0E996A /* AASerializable.swift */,
				CAB90023495AC282248E0C52BCC096FE /* AASeries.swift */,
				3482C9739866F370FDDB5D151C1C0EAA /* AASeriesElement.swift */,
				F7874C760A87744CAA853988F782C073 /* AAStates.swift */,
				94BC233410350C86C0CED0C676C1ADFF /* AAStyle.swift */,
				EFFB1E237E7530F28922CE86616BA63C /* AASubtitle.swift */,
				EF707DF0E6E12D4132FA0D865A48A643 /* AATitle.swift */,
				FD29EA9920196D0F190AA0EEFC6D22C8 /* AATooltip.swift */,
				DD4EDDCA524B6008B1568202AD30FF55 /* AAXAxis.swift */,
				601AB86DCBD6D93A0DDECA9D7A439CA6 /* AAYAxis.swift */,
				8FC0B5A0F9C08EA7D45C929BCDBE3E6A /* ProjectBundlePathLoader.swift */,
				7B37F2F38B240BA3D3A6B518E3489CDF /* Resources */,
				3A09BC7131DB907D7BA91926A65FE574 /* Support Files */,
			);
			name = AAInfographics;
			path = AAInfographics;
			sourceTree = "<group>";
		};
		8833ABD6DCF2AE34472CC664DFC3E326 /* Products */ = {
			isa = PBXGroup;
			children = (
				D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */,
				FC7F0CF2EA5DF59C59D9995890DA5C47 /* IQKeyboardCore */,
				6E897F35E5E27028AC58B14B552FBC8D /* IQKeyboardCore-IQKeyboardCore */,
				A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */,
				8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */,
				FEF9AC89E9C4BD37AC0AB78B535D1CFC /* IQKeyboardNotification */,
				120BD4C670EF00F4D5A40AA4B863A7AE /* IQKeyboardNotification-IQKeyboardNotification */,
				15848DEC7727A809830E7F6FC1B6CE0F /* IQKeyboardReturnManager */,
				110BD425B6CAD6801539E2C6AB6E0662 /* IQKeyboardReturnManager-IQKeyboardReturnManager */,
				B4B1A58096F15199A14069C5D6AA1C99 /* IQKeyboardToolbar */,
				A16FD016E18EACCF6B23F219F2E236FE /* IQKeyboardToolbar-IQKeyboardToolbar */,
				FA727BF5D9541C98C22C9ECC911576E2 /* IQKeyboardToolbarManager */,
				80A40EDA3AF0499BDBCFF09467BB49AD /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */,
				06E5B413FEA5A600D76636DA132F8FB9 /* IQTextInputViewNotification */,
				F956E149F5B195BB7833F97FC6211AE8 /* IQTextInputViewNotification-IQTextInputViewNotification */,
				0DFD4541FF9DAA31A2FC2A7F6D03ED22 /* IQTextView */,
				847044E56CBBCE1235A6F3CEF3F9F607 /* IQTextView-IQTextView */,
				9687765E0645223E6C71A362FDC12E69 /* Pods-PanHeatLog */,
				979486118B3E90C08386079D57962701 /* SnapKit */,
				B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8C2FF633D928EB6855B56FA5EA8945AD /* Resign */ = {
			isa = PBXGroup;
			children = (
				348809270222C7DE52AB3C7E86D3B420 /* IQKeyboardManager+Resign.swift */,
				5E0C87D0BAED94824B7D63B623525136 /* IQKeyboardManager+Resign_Deprecated.swift */,
				6F3916446A7F91567334620FD912480A /* IQKeyboardResignHandler.swift */,
				0CE8DA1F5D2E3376A4BB5C19BC8266CF /* IQKeyboardResignHandler+Internal.swift */,
				657344CEDA69CAFB356264F9737560F3 /* UIView+Resign.swift */,
				D6FB77CEB4BB69193BF43642804E943D /* UIView+ResignObjc.swift */,
			);
			name = Resign;
			sourceTree = "<group>";
		};
		96AAF3E9E4EF2615D2816BE70AFCF06F /* IQTextView */ = {
			isa = PBXGroup;
			children = (
				D7F9F789A5CF99CB8D929DEC42BE5FE0 /* IQTextView.swift */,
				5658F4EEFC55C9126531F11356D66B57 /* IQTextView+Placeholderable.swift */,
				610FDC795BB900F1DF176373A21CDB5D /* Resources */,
				511738346C4ADE89848CAF457B53DFC8 /* Support Files */,
			);
			name = IQTextView;
			path = IQTextView;
			sourceTree = "<group>";
		};
		96FF4A25939679EAD19176C94E938B7B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				6A947966428D212C3F51EA82F8F54068 /* IQKeyboardToolbar.modulemap */,
				BD2855DAAA3DAAEAC38313F8F695C1C0 /* IQKeyboardToolbar-dummy.m */,
				F2A725DAC8FDCE0DCD5231871B2F666E /* IQKeyboardToolbar-Info.plist */,
				3122AE7ACD0984D0DD5B88D292437588 /* IQKeyboardToolbar-prefix.pch */,
				298606B078D7DDFD635F774C76564B2A /* IQKeyboardToolbar-umbrella.h */,
				C1A405690A3BADC0EC66D4878DCBDE78 /* IQKeyboardToolbar.debug.xcconfig */,
				D07634E5CD15C4026C8D7B5AC5BE071A /* IQKeyboardToolbar.release.xcconfig */,
				BC196AF111EDFA1F3DB902002D47B239 /* ResourceBundle-IQKeyboardToolbar-IQKeyboardToolbar-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardToolbar";
			sourceTree = "<group>";
		};
		9729CA3017A0ADE69663276CBBFB0D75 /* Pods */ = {
			isa = PBXGroup;
			children = (
				7D566B63DFD80BF10F9E3FA7D255B360 /* AAInfographics */,
				C0BE678B9D2DDA1C72C86F3165EF11BB /* IQKeyboardCore */,
				798CC9C0B5C713DE53587C078DAEE453 /* IQKeyboardManagerSwift */,
				4081FFFBDD1B15DD33F699D976C4E967 /* IQKeyboardNotification */,
				97596234019E5D1DAF146DDBAED8F18F /* IQKeyboardReturnManager */,
				0DCB1FB9A1831977DAD21022AC6334EA /* IQKeyboardToolbar */,
				0D24BAA7C7C4F335A62182ACC73E9131 /* IQKeyboardToolbarManager */,
				E1E65DCDAF4FD5137DD2DC370C096EB5 /* IQTextInputViewNotification */,
				96AAF3E9E4EF2615D2816BE70AFCF06F /* IQTextView */,
				DA676C899299A22316B3AECE2DF3A957 /* SnapKit */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		97596234019E5D1DAF146DDBAED8F18F /* IQKeyboardReturnManager */ = {
			isa = PBXGroup;
			children = (
				6A5F0FC2865E55C9041F0D159EDFBF65 /* IQKeyboardReturnManager.swift */,
				42CEB7C81D117C4CEF3E789414624D15 /* IQKeyboardReturnManager+UITextFieldDelegate.swift */,
				3D789E6F49AC836683A7EC86DF5AACBD /* IQKeyboardReturnManager+UITextViewDelegate.swift */,
				C7B9C2EBA0232D5E1D8BE46CA45D61B6 /* IQTextInputViewInfoModel.swift */,
				72BA2519E40011ECD9557813468E874D /* Resources */,
				9C754814B3EC9DDC7B2045FA8AAD0B32 /* Support Files */,
			);
			name = IQKeyboardReturnManager;
			path = IQKeyboardReturnManager;
			sourceTree = "<group>";
		};
		991272B67CBC2B671082F1068013A972 /* Pods-PanHeatLog */ = {
			isa = PBXGroup;
			children = (
				D4D4D009750A1AD25BD25A9526E010FE /* Pods-PanHeatLog.modulemap */,
				7363301F8F78D0693B1A537144F5D9E1 /* Pods-PanHeatLog-acknowledgements.markdown */,
				7BE69FE2D9A5CCA451444EF57551EF68 /* Pods-PanHeatLog-acknowledgements.plist */,
				3DBAA21826CE786A6FB19EC96209065A /* Pods-PanHeatLog-dummy.m */,
				2B43D65C2303789C650E1D27E3FEF29D /* Pods-PanHeatLog-frameworks.sh */,
				F0AE43462ADB1ABE08573711AAFCB412 /* Pods-PanHeatLog-Info.plist */,
				CB09FEEB59330C413D490AA0684E7121 /* Pods-PanHeatLog-umbrella.h */,
				7F7B660C82F68382BA2E6CDC1025E768 /* Pods-PanHeatLog.debug.xcconfig */,
				B69B12F4F85CCE80420D350C3A0C2B42 /* Pods-PanHeatLog.release.xcconfig */,
			);
			name = "Pods-PanHeatLog";
			path = "Target Support Files/Pods-PanHeatLog";
			sourceTree = "<group>";
		};
		9C754814B3EC9DDC7B2045FA8AAD0B32 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				843572B8598B64E905321BD82DE53950 /* IQKeyboardReturnManager.modulemap */,
				4882C6E7B2368B2E0DF8BBC737A360F0 /* IQKeyboardReturnManager-dummy.m */,
				09ADB94D9386E3D4C698644E7E9F33F5 /* IQKeyboardReturnManager-Info.plist */,
				82D7EBCCCDA4531DCA2056AD896E7DB0 /* IQKeyboardReturnManager-prefix.pch */,
				56390A71C9082EEE2E7FBA0576CCA9E5 /* IQKeyboardReturnManager-umbrella.h */,
				50DF48E3B8BC217CE8D63EFA9E6B66E2 /* IQKeyboardReturnManager.debug.xcconfig */,
				7CC0E4A354F42DA837785C0F3C6CC1C1 /* IQKeyboardReturnManager.release.xcconfig */,
				D80AAFC640E43FBB9B64F9F605DFB992 /* ResourceBundle-IQKeyboardReturnManager-IQKeyboardReturnManager-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardReturnManager";
			sourceTree = "<group>";
		};
		9F43688A7B42F0BE7402E65B55F77D80 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D2AD956314FDCC72925D526682EA5060 /* IQKeyboardCore.modulemap */,
				1700FC3D5E527026F59B2F1B50D6111C /* IQKeyboardCore-dummy.m */,
				7D51AF377E9A26C60377B28CB8CFB8E3 /* IQKeyboardCore-Info.plist */,
				6A58ED3772A6B8BC1644BED01DE89051 /* IQKeyboardCore-prefix.pch */,
				AC884F7670E71798C9D9E703C3EF3C9E /* IQKeyboardCore-umbrella.h */,
				5125BBAA8DBB347FE5B8A5C95269F38C /* IQKeyboardCore.debug.xcconfig */,
				1B2DAA31000D78F8C0672BCE1F88EC38 /* IQKeyboardCore.release.xcconfig */,
				DE1F805FD7CE70CC220C2E16C7CB6812 /* ResourceBundle-IQKeyboardCore-IQKeyboardCore-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardCore";
			sourceTree = "<group>";
		};
		A713029B1951094E5FBF833FECB37522 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1013C65E0C9C59C8FD796EB3B1EFB2B2 /* IQKeyboardNotification.modulemap */,
				508C2D5B5FEE438A581CAD8679555DA3 /* IQKeyboardNotification-dummy.m */,
				736B348BBD81DF75BD1B5F156A86EDC5 /* IQKeyboardNotification-Info.plist */,
				0DAC4DD3569D55DC9D1B54A3FFB71721 /* IQKeyboardNotification-prefix.pch */,
				A63976351E9DFFA426D7F72A3C6A889C /* IQKeyboardNotification-umbrella.h */,
				74DFB4BCAAEE60F0E62E35E3C000585A /* IQKeyboardNotification.debug.xcconfig */,
				CA4A813BF5DB47478696D10E21404434 /* IQKeyboardNotification.release.xcconfig */,
				6F2A7C031B8CB97E1A421061C2A3F74B /* ResourceBundle-IQKeyboardNotification-IQKeyboardNotification-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardNotification";
			sourceTree = "<group>";
		};
		AB7CA5CCABDC874067DC7451EC386018 /* Core */ = {
			isa = PBXGroup;
			children = (
				D72A89474D6BF0C32331C67C0B7BF330 /* IQBarButtonItem.swift */,
				A9A6468F7162CFDB1E2A915BC2A7AABC /* IQBarButtonItemConfiguration.swift */,
				8E3AAB4E186B6B347149E82678971AAA /* IQInvocation.swift */,
				A09B54B4FA58970EC2E3729B35852BA4 /* IQKeyboardToolbar.swift */,
				AFAD35705E7E63E277B77F60601645AC /* IQKeyboardToolbarPlaceholderConfiguration.swift */,
				3EF2F873573308010090B3DF65B1856E /* IQTitleBarButtonItem.swift */,
				D44098B400C6237BCB2DB9A1BAEA0156 /* UIView+IQKeyboardExtension.swift */,
				C1B979E784BD86C84518FDE83B0C8BBD /* UIView+IQKeyboardExtensionDeprecated.swift */,
				00D46B9997092B2A102E596AC243131B /* UIView+IQKeyboardExtensionObjc.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		B7ABEC042C252C916AD1C8F4EAA2F88E /* Support Files */ = {
			isa = PBXGroup;
			children = (
				A3ABED541C599386BF4D0680CBA5F957 /* IQKeyboardToolbarManager.modulemap */,
				27C90F76847037B3D82100E2D1351F55 /* IQKeyboardToolbarManager-dummy.m */,
				DE79F8FE13CCBBEF1969783C4AD6B554 /* IQKeyboardToolbarManager-Info.plist */,
				92EF8B693A48AC488561735A70C3F318 /* IQKeyboardToolbarManager-prefix.pch */,
				EA978FF06E69B1F4D1A86C3901F308F3 /* IQKeyboardToolbarManager-umbrella.h */,
				3F6FFB4432E607EEA8C53ACCBCEBE58C /* IQKeyboardToolbarManager.debug.xcconfig */,
				612C5E6191A78693DBD595DBB136A790 /* IQKeyboardToolbarManager.release.xcconfig */,
				D29939705CA697DBB7BF226049895858 /* ResourceBundle-IQKeyboardToolbarManager-IQKeyboardToolbarManager-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardToolbarManager";
			sourceTree = "<group>";
		};
		BA008EFC3C52EEE8733940C8D58E071F /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E43B1CFB668D2CE8D92609D9BCA179DC /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */,
				5157ADB6CC75B2D9E888192341BC899D /* SnapKit.modulemap */,
				16F5CA87085999D67D77C5E61D264F51 /* SnapKit-dummy.m */,
				5F3F17006921F11C6592743383122554 /* SnapKit-Info.plist */,
				85146CF9F27897BBC9323631A61D3819 /* SnapKit-prefix.pch */,
				C73AC6DE755A26665E1258A212D465CB /* SnapKit-umbrella.h */,
				FFEC1C2CF15F9BCC07A92DE61FDBBE85 /* SnapKit.debug.xcconfig */,
				B15A7CEA361315CC78E49A788C43A09B /* SnapKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SnapKit";
			sourceTree = "<group>";
		};
		C0BE678B9D2DDA1C72C86F3165EF11BB /* IQKeyboardCore */ = {
			isa = PBXGroup;
			children = (
				2B9C7DBB05EA3F4068976B6D2EF9028A /* IQKeyboardConstants.swift */,
				3668CBA336295C38ADE69ECCD143536E /* IQKeyboardExtended.swift */,
				88584AC791F3FC51C93C5F6168E4BBCE /* IQTextInputView.swift */,
				1C03B4EB8E2D28BCD81EEDC51CEB9D0A /* UIView+Hierarchy.swift */,
				D94AEF7046C0EA1829687C068116493D /* Resources */,
				9F43688A7B42F0BE7402E65B55F77D80 /* Support Files */,
			);
			name = IQKeyboardCore;
			path = IQKeyboardCore;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				03C5C200A0787E300053CFA8F53CA094 /* Frameworks */,
				9729CA3017A0ADE69663276CBBFB0D75 /* Pods */,
				8833ABD6DCF2AE34472CC664DFC3E326 /* Products */,
				EB29E012ECFF3B7A28C4D2C403A04BCE /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D94AEF7046C0EA1829687C068116493D /* Resources */ = {
			isa = PBXGroup;
			children = (
				02BF6CF287EAC01539B309678BF5D047 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		DA676C899299A22316B3AECE2DF3A957 /* SnapKit */ = {
			isa = PBXGroup;
			children = (
				59E23ED8B58466857D8154939DD5CBA2 /* Constraint.swift */,
				3412B730DD07DEA9D2DDF4CBE885B6A0 /* ConstraintAttributes.swift */,
				FB2BE84611FD57296289A2176432D467 /* ConstraintConfig.swift */,
				B8060CF2906C11EB335B31365487D03B /* ConstraintConstantTarget.swift */,
				6E437448F2FEF03EF2C18A3F8C0953EA /* ConstraintDescription.swift */,
				24FE5E6336D23CF3687EB2B3BB263460 /* ConstraintDirectionalInsets.swift */,
				199DF50BC7E7EABE653BD7B71217F612 /* ConstraintDirectionalInsetTarget.swift */,
				4C71D91972189FD2777485491A37C95E /* ConstraintDSL.swift */,
				7F74465262F2A5ED89D7019381AB2C65 /* ConstraintInsets.swift */,
				0346AE155F9256D138EA5817E9EA42C1 /* ConstraintInsetTarget.swift */,
				C798641FDEEBA056B822BCE59F63D664 /* ConstraintItem.swift */,
				928C5B1D5F6DFFE53DD010204B29B432 /* ConstraintLayoutGuide.swift */,
				668DAF8106B28CF265329E3EF262870E /* ConstraintLayoutGuide+Extensions.swift */,
				633F22429340E42E3DD1FD0492BB608D /* ConstraintLayoutGuideDSL.swift */,
				67E55C1AA82152BAB7A54C3382BA404D /* ConstraintLayoutSupport.swift */,
				00EA741E8AF045FAC8F528905942C848 /* ConstraintLayoutSupportDSL.swift */,
				33D14A15223B424596A94B0FF9AD0455 /* ConstraintMaker.swift */,
				8494813C615A022BA8CFE8233CBFA1EB /* ConstraintMakerEditable.swift */,
				FC981B408A12DCABF5A8DB404D1CEAB6 /* ConstraintMakerExtendable.swift */,
				2144EF52672DE8BA67D557BEF1E54916 /* ConstraintMakerFinalizable.swift */,
				9A637B309721E6ED448681E8B7E93C95 /* ConstraintMakerPrioritizable.swift */,
				36B1F6B1B79CA498A42C2ADC7A2CC1C5 /* ConstraintMakerRelatable.swift */,
				08A8A6258AFFAC6569C0C9628AF06CCF /* ConstraintMakerRelatable+Extensions.swift */,
				3C9B0BF1A36337AF4F14010ADC211404 /* ConstraintMultiplierTarget.swift */,
				169ADC49122B3EDC175B048540C3BEB5 /* ConstraintOffsetTarget.swift */,
				948DF3A5E94617F57EA42D7431440372 /* ConstraintPriority.swift */,
				EE86430300492B0CC41E0DE8C538F767 /* ConstraintPriorityTarget.swift */,
				5F77921D76830A02D5C519AFDF88F8DB /* ConstraintRelatableTarget.swift */,
				8D80896F510609A0DCD985AF468FEFA4 /* ConstraintRelation.swift */,
				4493ED595B1208F5551ACE09D6AFCDD8 /* ConstraintView.swift */,
				830802554E19B262E801B1C9FE1C4FA0 /* ConstraintView+Extensions.swift */,
				E2312813D00F1457F9768DF3B98D3416 /* ConstraintViewDSL.swift */,
				EAA0306563071806EE0AC0EF961C1EC4 /* Debugging.swift */,
				655F4FDC82F6C0710B486E06C945A46B /* LayoutConstraint.swift */,
				4A6D59042DEA36191C9C276ECC305E7C /* LayoutConstraintItem.swift */,
				F7DF026B4D66643B763EDED020D892B6 /* Typealiases.swift */,
				E1DD76BFB2BF9C8EAE5F4B7C39F48830 /* UILayoutSupport+Extensions.swift */,
				F0CBFC928D8D7F1E93B597A641F3D98A /* Resources */,
				BA008EFC3C52EEE8733940C8D58E071F /* Support Files */,
			);
			name = SnapKit;
			path = SnapKit;
			sourceTree = "<group>";
		};
		DAFC6F3C9374760DC1B7FD4F59D0D3D1 /* Core */ = {
			isa = PBXGroup;
			children = (
				2CD4053154999DA33EBDDD68A141CBC2 /* IQActiveConfiguration.swift */,
				4DDB6924DFF09E09ABCD1E7684B7CEF2 /* IQKeyboardManager.swift */,
				5C550CE3F7553BF8B8E38E3228FAA7F1 /* IQKeyboardManager+ActiveConfiguration.swift */,
				3864A403EE6597B043FFCDFE05044EFF /* IQKeyboardManager+Debug.swift */,
				536FA061AB292A160328537ED19FC2EA /* IQKeyboardManager+Deprecated.swift */,
				49C5925185C517D916C11E06F5A63A04 /* IQKeyboardManager+Internal.swift */,
				06027039BC4148C2943618E6B574D1C8 /* IQKeyboardManager+Position.swift */,
				DCB49FA9B1BCE2864D36EBD414A73750 /* IQRootControllerConfiguration.swift */,
				AC43A481432B3B73A1EEA48C783F41D9 /* IQScrollViewConfiguration.swift */,
				33CB043EE7B28223351E823564E71F0A /* UICollectionView+IndexPaths.swift */,
				DA835EA2C8E9C9738591E70E5BE312F7 /* UIScrollView+IQKeyboardManagerExtension.swift */,
				2308CD4FE177F6A0557DDA9F54AA2CB6 /* UIScrollView+IQKeyboardManagerExtensionObjc.swift */,
				C9F44BDBEB3196ED66C3B0A763045FFE /* UITableView+IndexPaths.swift */,
				927E2BCC33602AEC64F803D9840B66B1 /* UIView+IQKeyboardManagerExtension.swift */,
				36520879CF36902738D425E3699619CF /* UIView+IQKeyboardManagerExtensionObjc.swift */,
				A4D799E2F764D99DE9D7FCF7E78A8343 /* UIView+Parent.swift */,
				6464E25338230129AFA9AC7855F81506 /* UIView+ParentObjc.swift */,
				5726B4B5A35E9D5581EA681DBC3CAE57 /* UIViewController+ParentContainer.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		E1E65DCDAF4FD5137DD2DC370C096EB5 /* IQTextInputViewNotification */ = {
			isa = PBXGroup;
			children = (
				8222A3F28CC4ECA814C18BE20495A24F /* IQTextInputViewInfo.swift */,
				06D3394507D9AE790950D1AFE5D6BBF7 /* IQTextInputViewNotification.swift */,
				14C5274507F9F61E112A8255A04ED441 /* Resources */,
				E381BBE5658E7E55A3B44B4480834655 /* Support Files */,
			);
			name = IQTextInputViewNotification;
			path = IQTextInputViewNotification;
			sourceTree = "<group>";
		};
		E381BBE5658E7E55A3B44B4480834655 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				01E069A0FED2D32BAF8408D14A052CF1 /* IQTextInputViewNotification.modulemap */,
				F5ACEF7B197A86A54BB2C7D3B4124C78 /* IQTextInputViewNotification-dummy.m */,
				255BF8D7047D07499B252CA15AB0E7EF /* IQTextInputViewNotification-Info.plist */,
				B365E882244C1431F3CC69B6C042CBDD /* IQTextInputViewNotification-prefix.pch */,
				930663BF88BD59782FE618D46E4CA1DE /* IQTextInputViewNotification-umbrella.h */,
				F9450613277EED51A0DE3A784EF4A0D3 /* IQTextInputViewNotification.debug.xcconfig */,
				1D4C859E7799D93FC39352806DF3050C /* IQTextInputViewNotification.release.xcconfig */,
				4415F8C21967BB4909463B91C482BBD3 /* ResourceBundle-IQTextInputViewNotification-IQTextInputViewNotification-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQTextInputViewNotification";
			sourceTree = "<group>";
		};
		EB29E012ECFF3B7A28C4D2C403A04BCE /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				991272B67CBC2B671082F1068013A972 /* Pods-PanHeatLog */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		F0CBFC928D8D7F1E93B597A641F3D98A /* Resources */ = {
			isa = PBXGroup;
			children = (
				C1EF70B6DEEA933E42F8C6C925410749 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		18F1454F92AB197091896780774AFD7B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				98F06146313DF71B8F040D7C9E81C3ED /* IQKeyboardNotification-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		376FD0142D265B48D990025F0F8D16A8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				49777AA7557617FEF959EEF1C145CCE5 /* IQKeyboardToolbar-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B1AF30CF9C8E42E24DA5BEA63D1D465 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FE0D3260BF27D388B0200F1AC8151619 /* IQKeyboardCore-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B72FFD9AC7CF7F75F8E8A12F8FDFF90 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A335EBC0BF593E5DBC473E43CFB2199D /* IQTextView-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		401E3F8EBADAB63D25145022E819341B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8959C17DDE29435F37374FF8D9FCCDB /* Pods-PanHeatLog-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4E447730924F1AD868BF79028A254970 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FBE2A348B6266D647CCEC706D32FE1A2 /* IQTextInputViewNotification-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B1500FE995B9224E0AF0B42CE93C03B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C08BBEDF18AE8DF38ABA50C42B1D2E2C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9298CB7C2E4AC4E751E535DA15BEEFA8 /* IQKeyboardManagerSwift-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D60F5D10A589971C127766407B310D89 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0ECDECBD58B97546E720720259A7BAD1 /* IQKeyboardToolbarManager-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DABE417CE03DDD06AC054920CFA90A5F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A11DEF54C75555C0F7DA3230FBFA487B /* IQKeyboardReturnManager-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9A07214E553E4E24A61297849873F92 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******************************** /* AAInfographics-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		020993F16DA5986DACE118349EBCE9E5 /* IQKeyboardToolbarManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 760610724531E6588FC50CE6A17DADEB /* Build configuration list for PBXNativeTarget "IQKeyboardToolbarManager" */;
			buildPhases = (
				D60F5D10A589971C127766407B310D89 /* Headers */,
				D24D3355A758B0E64E469A79879149C8 /* Sources */,
				7133E7E2A54488929356B41F5E5ABC1C /* Frameworks */,
				4B5863C53D5C694F29DABB53524230D0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8A7EE866F60F486BBFAD86C9DEB3FA34 /* PBXTargetDependency */,
				BCD149D95B63F6C1728E21E813EE69C0 /* PBXTargetDependency */,
				0F5CA94357F9486CB92418311E4BA53D /* PBXTargetDependency */,
			);
			name = IQKeyboardToolbarManager;
			productName = IQKeyboardToolbarManager;
			productReference = FA727BF5D9541C98C22C9ECC911576E2 /* IQKeyboardToolbarManager */;
			productType = "com.apple.product-type.framework";
		};
		0981F89DB5DA3FFCFFEBDE1F56287054 /* IQKeyboardReturnManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF179C10572D282D3F2E796E6329C713 /* Build configuration list for PBXNativeTarget "IQKeyboardReturnManager" */;
			buildPhases = (
				DABE417CE03DDD06AC054920CFA90A5F /* Headers */,
				410A8E7566CB6A63D50DC7DEFCD799AD /* Sources */,
				B4742DD99303DB4F32189A0648880485 /* Frameworks */,
				BBD2A1059763D3478C8A25DEBE381BBC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				89E6403A63382A560D8BC7C3B2396ED8 /* PBXTargetDependency */,
				62C8BD725B455FE0DDD9A54EFFC4B753 /* PBXTargetDependency */,
			);
			name = IQKeyboardReturnManager;
			productName = IQKeyboardReturnManager;
			productReference = 15848DEC7727A809830E7F6FC1B6CE0F /* IQKeyboardReturnManager */;
			productType = "com.apple.product-type.framework";
		};
		12890DE3ABBC2CA295E108358D85EE69 /* IQTextView */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BB7046F2A56F7CB32A0EA10728C363C /* Build configuration list for PBXNativeTarget "IQTextView" */;
			buildPhases = (
				3B72FFD9AC7CF7F75F8E8A12F8FDFF90 /* Headers */,
				5E275816AC81E669E77C24EF3FB87B39 /* Sources */,
				8FE3BA34FC02FA47059B5BFF66372A82 /* Frameworks */,
				77582F0D89EA903E74CDD1BEB8E745B2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FA82F8E10B7DBC2B3888E6A2F90C4300 /* PBXTargetDependency */,
				DA0FEC58B6D01766C8D7E548B77A74F1 /* PBXTargetDependency */,
			);
			name = IQTextView;
			productName = IQTextView;
			productReference = 0DFD4541FF9DAA31A2FC2A7F6D03ED22 /* IQTextView */;
			productType = "com.apple.product-type.framework";
		};
		19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */;
			buildPhases = (
				5B1500FE995B9224E0AF0B42CE93C03B /* Headers */,
				F7AC6792C89443C7B212A06E810BAB97 /* Sources */,
				33428AC36668E3ED52DB70316F843FB8 /* Frameworks */,
				1DEDF411E550D85A1218E1655456A9CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				78E643933ECB6E7C467815CB5B648FC6 /* PBXTargetDependency */,
			);
			name = SnapKit;
			productName = SnapKit;
			productReference = 979486118B3E90C08386079D57962701 /* SnapKit */;
			productType = "com.apple.product-type.framework";
		};
		283C1F2EA88CD4413165801A6748A48E /* IQTextInputViewNotification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BB26072735112C4E5E4EF5DFD0C82653 /* Build configuration list for PBXNativeTarget "IQTextInputViewNotification" */;
			buildPhases = (
				4E447730924F1AD868BF79028A254970 /* Headers */,
				DAF47D3F10E684F5CDD9673F6661C9C9 /* Sources */,
				93193587BB52300D42937FD1B62BD7D7 /* Frameworks */,
				DADBDE212ED29B6DE68C7F2B816B7C55 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9D586C8C432C2F76EC6CDB67709D1E05 /* PBXTargetDependency */,
				CEABB35AE65DED58953358035B5149FC /* PBXTargetDependency */,
			);
			name = IQTextInputViewNotification;
			productName = IQTextInputViewNotification;
			productReference = 06E5B413FEA5A600D76636DA132F8FB9 /* IQTextInputViewNotification */;
			productType = "com.apple.product-type.framework";
		};
		2B8FF445A5162845FAB9EC00FC92B694 /* IQKeyboardNotification-IQKeyboardNotification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5B957002C814061E6565BAE4FD99792F /* Build configuration list for PBXNativeTarget "IQKeyboardNotification-IQKeyboardNotification" */;
			buildPhases = (
				40D9FE8AB1FF4389CA979364171EC88A /* Sources */,
				84653E0AC28BE9DC3203CA0F84F23663 /* Frameworks */,
				3BEE1559F9082BD3D1BFE745E6AB5E93 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardNotification-IQKeyboardNotification";
			productName = IQKeyboardNotification;
			productReference = 120BD4C670EF00F4D5A40AA4B863A7AE /* IQKeyboardNotification-IQKeyboardNotification */;
			productType = "com.apple.product-type.bundle";
		};
		4502C7427440BEB17A50C0BF6E638A85 /* IQTextInputViewNotification-IQTextInputViewNotification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9EFE37ED5928B072A638BAEECD54E19 /* Build configuration list for PBXNativeTarget "IQTextInputViewNotification-IQTextInputViewNotification" */;
			buildPhases = (
				628275C08C534BC8C85B3F973CA9DBAA /* Sources */,
				E69AFA77E71336D6ED67A4FFDEFB9C14 /* Frameworks */,
				B99467F0DCEC72CE9B562BF76E2E0D66 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQTextInputViewNotification-IQTextInputViewNotification";
			productName = IQTextInputViewNotification;
			productReference = F956E149F5B195BB7833F97FC6211AE8 /* IQTextInputViewNotification-IQTextInputViewNotification */;
			productType = "com.apple.product-type.bundle";
		};
		58AF714A0D941CAB7E820C861AF461C0 /* Pods-PanHeatLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 50002D22DCFE9A2A2D579EC37E1ABC28 /* Build configuration list for PBXNativeTarget "Pods-PanHeatLog" */;
			buildPhases = (
				401E3F8EBADAB63D25145022E819341B /* Headers */,
				70C5BD1C7A97B200B19AA22A5021A035 /* Sources */,
				925CB1889ABB4BCC5AD94E94C4BAE8C5 /* Frameworks */,
				44EC3B683FA2A5416490BD9CC469FDB8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				85DD305AA0DD96C9D8CE75A691D59493 /* PBXTargetDependency */,
				2356C57EBFA53E3764456DBFA724ECF5 /* PBXTargetDependency */,
				C0B7584C660FE9B60FDF9CC3C8F5F7D7 /* PBXTargetDependency */,
				6408129CCE62E6F110869250FB7DB83D /* PBXTargetDependency */,
				F10D1E3704249119E6845F7F2D875B9C /* PBXTargetDependency */,
				AF26DF1BF273C7D321F8271B3C7682FD /* PBXTargetDependency */,
				DF8A0A736C48AA317DA2B3943599BE0A /* PBXTargetDependency */,
				A1C36DEFAE03A079BEBC2F8C2868CA07 /* PBXTargetDependency */,
				1282C7D6BD37B9973E0FBC8D257F3458 /* PBXTargetDependency */,
				CD7CA76E2E954D03A800EE45ECFBCF05 /* PBXTargetDependency */,
			);
			name = "Pods-PanHeatLog";
			productName = Pods_PanHeatLog;
			productReference = 9687765E0645223E6C71A362FDC12E69 /* Pods-PanHeatLog */;
			productType = "com.apple.product-type.framework";
		};
		7C5613175BBC4BF67E36DB4FBEBC01D0 /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6BB3220D2825BD6888456AE177C7DBC2 /* Build configuration list for PBXNativeTarget "IQKeyboardToolbarManager-IQKeyboardToolbarManager" */;
			buildPhases = (
				7150A22CFEA5D02AE047ED3D93F2A57A /* Sources */,
				1ACABF250C77AFB5EC7A3D6284E4493C /* Frameworks */,
				6209CA1053BC4C43C90A0FB5F4DC8C39 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardToolbarManager-IQKeyboardToolbarManager";
			productName = IQKeyboardToolbarManager;
			productReference = 80A40EDA3AF0499BDBCFF09467BB49AD /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */;
			productType = "com.apple.product-type.bundle";
		};
		81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B0ADC8A5D956F58C0F317AF3DCD7B6A /* Build configuration list for PBXNativeTarget "AAInfographics" */;
			buildPhases = (
				F9A07214E553E4E24A61297849873F92 /* Headers */,
				97A30ADBBF6A936ADC56960019ED49B3 /* Sources */,
				79E123CE7C2BE00816E19CE19C99028D /* Frameworks */,
				84425CF1D06AAB924A07312122E7619A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AAInfographics;
			productName = AAInfographics;
			productReference = D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */;
			productType = "com.apple.product-type.framework";
		};
		88810798DA63A2F6611B0970EA276DEC /* IQKeyboardReturnManager-IQKeyboardReturnManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5F0B3B2A93E52C770D727217C1EC6443 /* Build configuration list for PBXNativeTarget "IQKeyboardReturnManager-IQKeyboardReturnManager" */;
			buildPhases = (
				C83EFDE64AC5283C415B11ECCBA5B150 /* Sources */,
				9A27C11FD69FE5449D243206A5A3F8BB /* Frameworks */,
				3C32F509B6A04913A961DBAED36F2C79 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardReturnManager-IQKeyboardReturnManager";
			productName = IQKeyboardReturnManager;
			productReference = 110BD425B6CAD6801539E2C6AB6E0662 /* IQKeyboardReturnManager-IQKeyboardReturnManager */;
			productType = "com.apple.product-type.bundle";
		};
		8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E12284EF50ABF442B8D88AC686108E1 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */;
			buildPhases = (
				2705BF829F2DF7ECE8DC2A69695A5767 /* Sources */,
				AD12BA8CA1D7E3FF2C044AB50A2259B6 /* Frameworks */,
				9036FD16DDEC0F4D42189D286715C81E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SnapKit-SnapKit_Privacy";
			productName = SnapKit_Privacy;
			productReference = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */;
			productType = "com.apple.product-type.bundle";
		};
		982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2FF989C844B9779E4D252C946FAE2517 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift-IQKeyboardManagerSwift" */;
			buildPhases = (
				6B1B7991753BA0C6214F9C677DCB138D /* Sources */,
				7F76A8B49DA44DBD28915B6F9C9B2FE8 /* Frameworks */,
				BDC601A1BB47D6E52AADF689211E9FD3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
			productName = IQKeyboardManagerSwift;
			productReference = 8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */;
			productType = "com.apple.product-type.bundle";
		};
		A26E6FD851C20D652B2755C1464A9990 /* IQKeyboardNotification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6E50B5B04FE20028F9E8935EA9CC5DFC /* Build configuration list for PBXNativeTarget "IQKeyboardNotification" */;
			buildPhases = (
				18F1454F92AB197091896780774AFD7B /* Headers */,
				E6678BFAB947B90F82373C4721F3A378 /* Sources */,
				2E0475FF91572EB671893970E3A33C31 /* Frameworks */,
				43AC6E950F89F76EEFFA6E8F1B45D5BC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E095D2AC38047A65E57C093380AF4FC7 /* PBXTargetDependency */,
			);
			name = IQKeyboardNotification;
			productName = IQKeyboardNotification;
			productReference = FEF9AC89E9C4BD37AC0AB78B535D1CFC /* IQKeyboardNotification */;
			productType = "com.apple.product-type.framework";
		};
		A6602BCAA6F4F932A586C41D0B7E019C /* IQTextView-IQTextView */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C66EEF1417828B16F28463A73C17310A /* Build configuration list for PBXNativeTarget "IQTextView-IQTextView" */;
			buildPhases = (
				108A6BB20A96A8862ED39638D6859C19 /* Sources */,
				29017F23FB7F0B09B9D1E6052EC1AA9D /* Frameworks */,
				8AA56BA042E673F26EAA348CE8EBBEE7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQTextView-IQTextView";
			productName = IQTextView;
			productReference = 847044E56CBBCE1235A6F3CEF3F9F607 /* IQTextView-IQTextView */;
			productType = "com.apple.product-type.bundle";
		};
		B247F77A0CD5E19C8187A9BA1EB58C09 /* IQKeyboardToolbar-IQKeyboardToolbar */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0A3763672A15AC97F3BA7516D6705609 /* Build configuration list for PBXNativeTarget "IQKeyboardToolbar-IQKeyboardToolbar" */;
			buildPhases = (
				B57217ED723331F45682F956BDED768F /* Sources */,
				380C287193A2B18646EC79EEBAF08144 /* Frameworks */,
				E8D49CF9103A483CBFFD7E00C79B0320 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardToolbar-IQKeyboardToolbar";
			productName = IQKeyboardToolbar;
			productReference = A16FD016E18EACCF6B23F219F2E236FE /* IQKeyboardToolbar-IQKeyboardToolbar */;
			productType = "com.apple.product-type.bundle";
		};
		B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3208C0F97FCB948C4F12E81E25FFEAF9 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift" */;
			buildPhases = (
				C08BBEDF18AE8DF38ABA50C42B1D2E2C /* Headers */,
				27DAA7793549B5BA83223EF9A017BFC2 /* Sources */,
				5B8C51C6BFA979E81A28179CCC2C0936 /* Frameworks */,
				636F2BA8305CAE418272BBDC04936516 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1C19105AFEC59C653FA1E3271CE39B85 /* PBXTargetDependency */,
				300C95D744C272C83E9AFCD5B869FAF9 /* PBXTargetDependency */,
				DDD7D2917FBBE628CC777A7D400D23D8 /* PBXTargetDependency */,
				1C90DBC7C8014BA126652DD2CDF17949 /* PBXTargetDependency */,
				26322097517E1091952E3E86B9668FDE /* PBXTargetDependency */,
				88881ECE852B4390054C6E2772A4443F /* PBXTargetDependency */,
			);
			name = IQKeyboardManagerSwift;
			productName = IQKeyboardManagerSwift;
			productReference = A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */;
			productType = "com.apple.product-type.framework";
		};
		EEE261386011CDF271BE289F73FF5959 /* IQKeyboardCore-IQKeyboardCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D8E48C460FD2B2F83D9E530EB0177D13 /* Build configuration list for PBXNativeTarget "IQKeyboardCore-IQKeyboardCore" */;
			buildPhases = (
				4526B81410171DE494B8B0A3FAB658D8 /* Sources */,
				4C94F3048E452D5CB9B37F4A099B5849 /* Frameworks */,
				FF407DEB7E86F773D9D1DE20EBD01D37 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardCore-IQKeyboardCore";
			productName = IQKeyboardCore;
			productReference = 6E897F35E5E27028AC58B14B552FBC8D /* IQKeyboardCore-IQKeyboardCore */;
			productType = "com.apple.product-type.bundle";
		};
		F4FE17428FD0E607723A44F17231B7A1 /* IQKeyboardToolbar */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 056B8246B82F6961CE9AB8AE61E6F06D /* Build configuration list for PBXNativeTarget "IQKeyboardToolbar" */;
			buildPhases = (
				376FD0142D265B48D990025F0F8D16A8 /* Headers */,
				370C97ED27E04D3E5F3A7B020BA27569 /* Sources */,
				4320A20732FD5CD14B63E2E51CC420DE /* Frameworks */,
				B0885C21068016C3407FB3B025B8CC1E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				05657628EF8D1932C37FAA1E2A06FB30 /* PBXTargetDependency */,
				DDE94D20A1A6C0DD3B7DBFBAB8809B95 /* PBXTargetDependency */,
			);
			name = IQKeyboardToolbar;
			productName = IQKeyboardToolbar;
			productReference = B4B1A58096F15199A14069C5D6AA1C99 /* IQKeyboardToolbar */;
			productType = "com.apple.product-type.framework";
		};
		F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B2130418C2FAD0A1E2FD872758AD609B /* Build configuration list for PBXNativeTarget "IQKeyboardCore" */;
			buildPhases = (
				3B1AF30CF9C8E42E24DA5BEA63D1D465 /* Headers */,
				BF6F1816B0325207E7310ABC9FD05174 /* Sources */,
				D07B79E83A18E3018EFA8CE654EF97CB /* Frameworks */,
				30325A914D6527C8259F46363D6D7531 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C520E6C9D1D62E28FD6FC3B41A6BBFA5 /* PBXTargetDependency */,
			);
			name = IQKeyboardCore;
			productName = IQKeyboardCore;
			productReference = FC7F0CF2EA5DF59C59D9995890DA5C47 /* IQKeyboardCore */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8833ABD6DCF2AE34472CC664DFC3E326 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */,
				F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */,
				EEE261386011CDF271BE289F73FF5959 /* IQKeyboardCore-IQKeyboardCore */,
				B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */,
				982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */,
				A26E6FD851C20D652B2755C1464A9990 /* IQKeyboardNotification */,
				2B8FF445A5162845FAB9EC00FC92B694 /* IQKeyboardNotification-IQKeyboardNotification */,
				0981F89DB5DA3FFCFFEBDE1F56287054 /* IQKeyboardReturnManager */,
				88810798DA63A2F6611B0970EA276DEC /* IQKeyboardReturnManager-IQKeyboardReturnManager */,
				F4FE17428FD0E607723A44F17231B7A1 /* IQKeyboardToolbar */,
				B247F77A0CD5E19C8187A9BA1EB58C09 /* IQKeyboardToolbar-IQKeyboardToolbar */,
				020993F16DA5986DACE118349EBCE9E5 /* IQKeyboardToolbarManager */,
				7C5613175BBC4BF67E36DB4FBEBC01D0 /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */,
				283C1F2EA88CD4413165801A6748A48E /* IQTextInputViewNotification */,
				4502C7427440BEB17A50C0BF6E638A85 /* IQTextInputViewNotification-IQTextInputViewNotification */,
				12890DE3ABBC2CA295E108358D85EE69 /* IQTextView */,
				A6602BCAA6F4F932A586C41D0B7E019C /* IQTextView-IQTextView */,
				58AF714A0D941CAB7E820C861AF461C0 /* Pods-PanHeatLog */,
				19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */,
				8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1DEDF411E550D85A1218E1655456A9CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		30325A914D6527C8259F46363D6D7531 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6DE9FD143CBC24075601F8FE2ADC4115 /* IQKeyboardCore-IQKeyboardCore in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3BEE1559F9082BD3D1BFE745E6AB5E93 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5CEF2BF3A64BD87187DC43501A64A9DD /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3C32F509B6A04913A961DBAED36F2C79 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				108B158BEE9DEEB452691092AD527853 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		43AC6E950F89F76EEFFA6E8F1B45D5BC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				85E4FFD2D3E76E2F2A02B954F2D5C752 /* IQKeyboardNotification-IQKeyboardNotification in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		44EC3B683FA2A5416490BD9CC469FDB8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B5863C53D5C694F29DABB53524230D0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF6B50CF0B84B2032987A351D1370D86 /* IQKeyboardToolbarManager-IQKeyboardToolbarManager in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6209CA1053BC4C43C90A0FB5F4DC8C39 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				794075641074D9F648585B37B851A44B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		636F2BA8305CAE418272BBDC04936516 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E6FE2596512201193E95FC356C6E3351 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77582F0D89EA903E74CDD1BEB8E745B2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66B3CF0FA3383D7380C9020AF49B7BD3 /* IQTextView-IQTextView in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		84425CF1D06AAB924A07312122E7619A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C40219960F480E093C1155A69D951C2C /* AAJSFiles.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AA56BA042E673F26EAA348CE8EBBEE7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				585C44BD5E0588DC7A73859A35EAA957 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9036FD16DDEC0F4D42189D286715C81E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28831A65A8FFE6D27BB19BD0BC031492 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B0885C21068016C3407FB3B025B8CC1E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28CC28DFC13346866DEB2EF10D7D52CF /* IQKeyboardToolbar-IQKeyboardToolbar in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B99467F0DCEC72CE9B562BF76E2E0D66 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBE9F66A30C6166BAB30367221072773 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBD2A1059763D3478C8A25DEBE381BBC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				946E96D8486BF7DAA6177B5D1E1C1655 /* IQKeyboardReturnManager-IQKeyboardReturnManager in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BDC601A1BB47D6E52AADF689211E9FD3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C8100B337EB10A4BE7207A038E0E619 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DADBDE212ED29B6DE68C7F2B816B7C55 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C86A9CFD3486DDE59025E8E46C6421F5 /* IQTextInputViewNotification-IQTextInputViewNotification in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8D49CF9103A483CBFFD7E00C79B0320 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF98F903BA88E29F2A6BB090AEE13E32 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FF407DEB7E86F773D9D1DE20EBD01D37 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D55B4DE112B7E622442F6BC03B6B0D1 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		108A6BB20A96A8862ED39638D6859C19 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2705BF829F2DF7ECE8DC2A69695A5767 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27DAA7793549B5BA83223EF9A017BFC2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3A6B20BEE4F7A5B5835DE5D1E509754E /* IQActiveConfiguration.swift in Sources */,
				FF020D50D3B0742EFED9C172269D9D83 /* IQKeyboardAppearanceConfiguration.swift in Sources */,
				E84C30FA4A382D9C330E87FB32C009AE /* IQKeyboardAppearanceManager.swift in Sources */,
				47C9A000C20E04EC69BFEEF3C6AC6256 /* IQKeyboardAppearanceManager+Internal.swift in Sources */,
				30D6F928FC08B13B5BFEEB154A2089C1 /* IQKeyboardManager.swift in Sources */,
				D6B15F2F110BC58F7635A9FD7A985C79 /* IQKeyboardManager+ActiveConfiguration.swift in Sources */,
				77F1B40C21EFF28350EDA6A12263B098 /* IQKeyboardManager+Appearance.swift in Sources */,
				154230695B4E3D61B8D2BD02DAA2CE91 /* IQKeyboardManager+Appearance_Deprecated.swift in Sources */,
				24637C51989929A62E5E75585544A14D /* IQKeyboardManager+Debug.swift in Sources */,
				769B4905AD35AD554AA9DE98DA8E4E11 /* IQKeyboardManager+Deprecated.swift in Sources */,
				EBF34312A1F64FE81987440230E5BC49 /* IQKeyboardManager+Internal.swift in Sources */,
				05F4664FC4EF89925161A064AA6481BB /* IQKeyboardManager+Position.swift in Sources */,
				91CA63A8C62222E89FBB8FA2B7FE9CDE /* IQKeyboardManager+Resign.swift in Sources */,
				C20ECE96A97688090D5FE186DCF7B5D9 /* IQKeyboardManager+Resign_Deprecated.swift in Sources */,
				AC22AD1E1EF9CD9D6E242F3630A9A012 /* IQKeyboardManager+ToolbarManager.swift in Sources */,
				9D22F88B7F1AF054C2BF025DFB767154 /* IQKeyboardManager+ToolbarManagerDeprecated.swift in Sources */,
				C0AD493AE4A8DC8FF960D753D6D971E3 /* IQKeyboardManagerSwift-dummy.m in Sources */,
				8CBC72065F0261B373B958696985AF99 /* IQKeyboardResignHandler.swift in Sources */,
				C7C6F1AD0C44C79C4A3416FCB32B0233 /* IQKeyboardResignHandler+Internal.swift in Sources */,
				332EB3263B1002A5A0A146CCE70501AD /* IQRootControllerConfiguration.swift in Sources */,
				7CBA95E07A492F7311C7DD8EDF0B4172 /* IQScrollViewConfiguration.swift in Sources */,
				A6F1EFC3CDA009A4BE6CCBB01248DA21 /* UICollectionView+IndexPaths.swift in Sources */,
				40BB4A23AD4A18E81678F58EAA90EB85 /* UIScrollView+IQKeyboardManagerExtension.swift in Sources */,
				39B33D7873CD89C6A7E96ACD36A1EDFB /* UIScrollView+IQKeyboardManagerExtensionObjc.swift in Sources */,
				A983CCA5F6596F030EFCC1E27C1E766E /* UITableView+IndexPaths.swift in Sources */,
				2B067825708D13E7E455426389397D1A /* UIView+IQKeyboardManagerExtension.swift in Sources */,
				61ED64340371AB30B936A019C22C376D /* UIView+IQKeyboardManagerExtensionObjc.swift in Sources */,
				62B90C6FC83F91F91A4BF12022C7B050 /* UIView+Parent.swift in Sources */,
				E4A29F8C3ED424A7BC912DB796D03AFB /* UIView+ParentObjc.swift in Sources */,
				FD552610731EB624F8A47C3ACA927A15 /* UIView+Resign.swift in Sources */,
				355A2A3962A82AE25D219DB774CB6AEE /* UIView+ResignObjc.swift in Sources */,
				8C41EA954997897C3E30C5754010C05F /* UIViewController+ParentContainer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		370C97ED27E04D3E5F3A7B020BA27569 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3E18C78B7EE7A67C5F0EA37729E426D5 /* IQBarButtonItem.swift in Sources */,
				AB1CE4F61B10D7F11B3073AD4DDDEB8A /* IQBarButtonItemConfiguration.swift in Sources */,
				4FAF2E7DAF9B75AE20567FED3789F8E8 /* IQInvocation.swift in Sources */,
				ADE2069475CE82CF9D380EF4BB3FA06E /* IQKeyboardToolbar.swift in Sources */,
				974E63AA1E0055F59B28DF981D0AC674 /* IQKeyboardToolbar-dummy.m in Sources */,
				8FBD02B3DA24907BCC9C2027BA177E73 /* IQKeyboardToolbarPlaceholderConfiguration.swift in Sources */,
				31B7EF92F0B2C64A627F77E3ECB2C36E /* IQPlaceholderable.swift in Sources */,
				425740165A03C6C08E35C39415112D02 /* IQTitleBarButtonItem.swift in Sources */,
				D416EE12D2B866AFE127EF06EFEEC820 /* UIView+IQKeyboardExtension.swift in Sources */,
				CE45BBE9C0D24ADEC0F6985E0EFBF6E3 /* UIView+IQKeyboardExtensionDeprecated.swift in Sources */,
				E364BDD75ACDEF3302A44FC0D0459103 /* UIView+IQKeyboardExtensionObjc.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40D9FE8AB1FF4389CA979364171EC88A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		410A8E7566CB6A63D50DC7DEFCD799AD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F52AEF87130A1661D005DA57832273B7 /* IQKeyboardReturnManager.swift in Sources */,
				3B396DC2A9F9041CE91A70FA4F9AF034 /* IQKeyboardReturnManager+UITextFieldDelegate.swift in Sources */,
				9EA182CF7E8E54200088C50BA6156CD5 /* IQKeyboardReturnManager+UITextViewDelegate.swift in Sources */,
				32FFA15324CC5CB9103D5AAE40D3BACE /* IQKeyboardReturnManager-dummy.m in Sources */,
				0B20CBEC9B7CCB06F1D02DA9F75BE4C3 /* IQTextInputViewInfoModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4526B81410171DE494B8B0A3FAB658D8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5E275816AC81E669E77C24EF3FB87B39 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				76E282B34C2415294759290B55080C73 /* IQTextView.swift in Sources */,
				07C23039E4A4427150B28BD169CE5217 /* IQTextView+Placeholderable.swift in Sources */,
				F509F0508DF26AE4E831639DDC601832 /* IQTextView-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		628275C08C534BC8C85B3F973CA9DBAA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B1B7991753BA0C6214F9C677DCB138D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		70C5BD1C7A97B200B19AA22A5021A035 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A5475FFD60FDD956994E00B1EC1A4EEB /* Pods-PanHeatLog-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7150A22CFEA5D02AE047ED3D93F2A57A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97A30ADBBF6A936ADC56960019ED49B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C1207AB554753DD987E471DEA8E9DED /* AAAnimation.swift in Sources */,
				70A2FE54635EEBD101D5D79B01609C4D /* AAAxis.swift in Sources */,
				923CA1FDC87DDEA88E02067BECD83C78 /* AABoxplot.swift in Sources */,
				******************************** /* AABubbleLegend.swift in Sources */,
				261F1743BA33D3065C9FD7D3D7D85BED /* AAButtonTheme.swift in Sources */,
				90005505FB933A3047BE185F63A99FB3 /* AAChart.swift in Sources */,
				990BE6EFC195B60FCFD4BE108238E6D0 /* AAChartModel.swift in Sources */,
				0F2BF86610A292C81CEA284234E49055 /* AAChartView.swift in Sources */,
				21F8C522C6567324FEFF24831E3F4FA4 /* AAChartView+API.swift in Sources */,
				707F5D2C0DD51CE01C4BCD8ACAFDE4F4 /* AAChartViewPluginProvider.swift in Sources */,
				D86B9BE31D45BF36BE78559FBA4254E6 /* AAColor.swift in Sources */,
				B11B3A15203508AEF294D78D656AD8DA /* AAColumn.swift in Sources */,
				F98C041BD5C35C89C43DFBD4DF7EB712 /* AACredits.swift in Sources */,
				3E52BAFA9AECEF850B67B1E7C32650BF /* AACrosshair.swift in Sources */,
				353180A0F0DC4B61F2BDED206473D6F9 /* AADataLabels.swift in Sources */,
				E862E5D575BED4D94CE1D4F24D841192 /* AAExtension.swift in Sources */,
				23E7FD4F69645294729EB3839D524607 /* AAGradientColor.swift in Sources */,
				CCD99576883F2A882BE68EF947336D51 /* AAGradientColor+DefaultThemes.swift in Sources */,
				D8486770C951AD52186C9D105E060A60 /* AAGradientColor+LinearGradient.swift in Sources */,
				0895381789B493294369A187CF16097B /* AAGradientColor+RadialGradient.swift in Sources */,
				45459AF4B2FD64DD66FBA5889A811D9E /* AAInfographics-dummy.m in Sources */,
				6CBE440BE36788B5E56E7C6124B2455A /* AALabel.swift in Sources */,
				F52F7402A43AC9A8D04DEA11E1B802C6 /* AALabels.swift in Sources */,
				FF8555B8EC8BC27E2A2C22D5075BE646 /* AALang.swift in Sources */,
				B2B121881248EA5DF4A53AA03A6F40DE /* AALayoutAlgorithm.swift in Sources */,
				312C7FD0CFDC0D70C789F4107C37D627 /* AALegend.swift in Sources */,
				7C7D4E1F438258C4363547D5CD7826CE /* AAMarker.swift in Sources */,
				7F9F8BFE4A612F1EE0C0B1688185ED35 /* AAOptions.swift in Sources */,
				2F4337F8364DE531ADD9281A9F7CBA9F /* AAPackedbubble.swift in Sources */,
				48DF918490C82FE2849F2058784FCB79 /* AAPane.swift in Sources */,
				2CE3EDDFC3568BED3EBB743D36BD0A69 /* AAPie.swift in Sources */,
				0C7C78E9CA8DC47092FAF58E9FCA2A13 /* AAPlotBandsElement.swift in Sources */,
				858D141CDA8B7F21BCBE5CD8476F0F53 /* AAPlotLinesElement.swift in Sources */,
				28CF7A42811BA9B634621316DCAFFCC5 /* AAPlotOptions.swift in Sources */,
				356F2FE4EDACF39DAFA756E537ECD06B /* AAScatter.swift in Sources */,
				903685875CBD28FE7FBF09128948638E /* AAScrollablePlotArea.swift in Sources */,
				9BBD05F55C106CC86C80DA2DA985C2B3 /* AASerializable.swift in Sources */,
				4A84D7C091F340B22AAD8B88C1522160 /* AASeries.swift in Sources */,
				80E37F3FDB349D19B5DD572479F75315 /* AASeriesElement.swift in Sources */,
				1083DD9C24614A83BD0A843008DF366C /* AAStates.swift in Sources */,
				7359EA4AA9D0841330E2F010AD8F25A5 /* AAStyle.swift in Sources */,
				568C235842D9191E16FFB6BCC2DBF6ED /* AASubtitle.swift in Sources */,
				C46472E5D2A254C2EA873CEE3E9607D6 /* AATitle.swift in Sources */,
				513FF454FF96C8751781104F7A651A9B /* AATooltip.swift in Sources */,
				99B2134E59C2BAFCFD0EAD4E508007A1 /* AAXAxis.swift in Sources */,
				9963434D8659BEAE9B61E4E9E2A5C412 /* AAYAxis.swift in Sources */,
				7713C42CE43548B685917AA35DC638E9 /* ProjectBundlePathLoader.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B57217ED723331F45682F956BDED768F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BF6F1816B0325207E7310ABC9FD05174 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C945CB4EC42271D435BF23E7749BE6BF /* IQKeyboardConstants.swift in Sources */,
				01216C042B056FFF0FC7EE32A002C38A /* IQKeyboardCore-dummy.m in Sources */,
				378BD5280F6C07B11CF30B253D824972 /* IQKeyboardExtended.swift in Sources */,
				A39FE5F72261B202CC63722A7B357206 /* IQTextInputView.swift in Sources */,
				E1734E56DB48E6F238408C9A232C7051 /* UIView+Hierarchy.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C83EFDE64AC5283C415B11ECCBA5B150 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D24D3355A758B0E64E469A79879149C8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5C70A1A3F82408DA19F0E9CBB84D3023 /* Array+Sort.swift in Sources */,
				3C2B8C97374497F718B2D7BCC3458F17 /* IQDeepResponderContainerView.swift in Sources */,
				4A8C99371A914D0973745DA8124C217A /* IQKeyboardToolbarConfiguration.swift in Sources */,
				E076F42C12CB5D5A1F433625EC744CD8 /* IQKeyboardToolbarConstants.swift in Sources */,
				D45A98C2962636D86E92AFD1273145E9 /* IQKeyboardToolbarManager.swift in Sources */,
				0388214619F3AF5F5A122A6C8CF657C0 /* IQKeyboardToolbarManager+Action.swift in Sources */,
				45302C6ECBE939C4D1A647A03905D395 /* IQKeyboardToolbarManager+Debug.swift in Sources */,
				DFAC1DBC5816CA69B0D1450022F99099 /* IQKeyboardToolbarManager+Deprecated.swift in Sources */,
				2FE51C93DC6B9C0A99E451D138D0A1C8 /* IQKeyboardToolbarManager+Internal.swift in Sources */,
				D1B11F9CD7C549A01A38AC239F2DD42C /* IQKeyboardToolbarManager+Toolbar.swift in Sources */,
				AB892B4247E11F91F6F76E2EF16ABFAE /* IQKeyboardToolbarManager-dummy.m in Sources */,
				F70C920436626407BFB5139568A1DC51 /* UIView+Responders.swift in Sources */,
				424D364F1A2A84A922862D2B3BA97C29 /* UIView+RespondersObjc.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAF47D3F10E684F5CDD9673F6661C9C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F115D688A0C7F668669F03A924EECED5 /* IQTextInputViewInfo.swift in Sources */,
				A7A915DE430C29728CF9C7730BC1FB26 /* IQTextInputViewNotification.swift in Sources */,
				85444D2EC8165B8F85D1BFF78B183F97 /* IQTextInputViewNotification-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E6678BFAB947B90F82373C4721F3A378 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A95CCFD3E1129ADFA374308FC40B16CE /* IQKeyboardInfo.swift in Sources */,
				005BFA80B78D4801EC762BA4AC941E6E /* IQKeyboardNotification.swift in Sources */,
				D170E4225C7FE88040733CF1737D3FC0 /* IQKeyboardNotification-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7AC6792C89443C7B212A06E810BAB97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */,
				0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */,
				F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */,
				CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */,
				7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */,
				1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */,
				E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */,
				868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */,
				AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */,
				2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */,
				5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */,
				59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */,
				E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */,
				3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */,
				B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */,
				064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */,
				4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */,
				C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */,
				D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */,
				B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */,
				AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */,
				BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */,
				8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */,
				883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */,
				3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */,
				09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */,
				DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */,
				C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */,
				ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */,
				86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */,
				0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */,
				7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */,
				AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */,
				BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */,
				C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */,
				BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */,
				C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */,
				F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		05657628EF8D1932C37FAA1E2A06FB30 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardCore;
			target = F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */;
			targetProxy = 93A5F87A41307950FA6204EC7EA003C3 /* PBXContainerItemProxy */;
		};
		0F5CA94357F9486CB92418311E4BA53D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQTextInputViewNotification;
			target = 283C1F2EA88CD4413165801A6748A48E /* IQTextInputViewNotification */;
			targetProxy = FEF48ED8A7156196C864F8C49DFFD813 /* PBXContainerItemProxy */;
		};
		1282C7D6BD37B9973E0FBC8D257F3458 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQTextView;
			target = 12890DE3ABBC2CA295E108358D85EE69 /* IQTextView */;
			targetProxy = D328F8F9AA74F66F5C1157E758708DD8 /* PBXContainerItemProxy */;
		};
		1C19105AFEC59C653FA1E3271CE39B85 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
			target = 982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */;
			targetProxy = 82A22D6A3149269639DDE19497D57F32 /* PBXContainerItemProxy */;
		};
		1C90DBC7C8014BA126652DD2CDF17949 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardToolbarManager;
			target = 020993F16DA5986DACE118349EBCE9E5 /* IQKeyboardToolbarManager */;
			targetProxy = 94DB6F154A3F6AD33BDFC299B0718BFE /* PBXContainerItemProxy */;
		};
		2356C57EBFA53E3764456DBFA724ECF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardCore;
			target = F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */;
			targetProxy = 14C5B64A34A875BE8CB3757966FB3433 /* PBXContainerItemProxy */;
		};
		26322097517E1091952E3E86B9668FDE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQTextInputViewNotification;
			target = 283C1F2EA88CD4413165801A6748A48E /* IQTextInputViewNotification */;
			targetProxy = B8F37B61BBAF70451995806FE5429DC4 /* PBXContainerItemProxy */;
		};
		300C95D744C272C83E9AFCD5B869FAF9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardNotification;
			target = A26E6FD851C20D652B2755C1464A9990 /* IQKeyboardNotification */;
			targetProxy = 1E9DC4658CA148739A46B8755068F72D /* PBXContainerItemProxy */;
		};
		62C8BD725B455FE0DDD9A54EFFC4B753 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardReturnManager-IQKeyboardReturnManager";
			target = 88810798DA63A2F6611B0970EA276DEC /* IQKeyboardReturnManager-IQKeyboardReturnManager */;
			targetProxy = 848D8D6D0161C281A7221C34C402F0BC /* PBXContainerItemProxy */;
		};
		6408129CCE62E6F110869250FB7DB83D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardNotification;
			target = A26E6FD851C20D652B2755C1464A9990 /* IQKeyboardNotification */;
			targetProxy = 87CA28E7483E02B7F4B9DB460302DCAF /* PBXContainerItemProxy */;
		};
		78E643933ECB6E7C467815CB5B648FC6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SnapKit-SnapKit_Privacy";
			target = 8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */;
			targetProxy = C72938D14938B642BD79003FC250F9CA /* PBXContainerItemProxy */;
		};
		85DD305AA0DD96C9D8CE75A691D59493 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AAInfographics;
			target = 81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */;
			targetProxy = F42823EB4328B8AEE5EF5150AE308532 /* PBXContainerItemProxy */;
		};
		88881ECE852B4390054C6E2772A4443F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQTextView;
			target = 12890DE3ABBC2CA295E108358D85EE69 /* IQTextView */;
			targetProxy = ACBA97825DAF02F206C1328DA2C798EC /* PBXContainerItemProxy */;
		};
		89E6403A63382A560D8BC7C3B2396ED8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardCore;
			target = F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */;
			targetProxy = C4D9378512695DF093901755B0C95CC6 /* PBXContainerItemProxy */;
		};
		8A7EE866F60F486BBFAD86C9DEB3FA34 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardToolbar;
			target = F4FE17428FD0E607723A44F17231B7A1 /* IQKeyboardToolbar */;
			targetProxy = A27358DBD154416D9730492A111858B1 /* PBXContainerItemProxy */;
		};
		9D586C8C432C2F76EC6CDB67709D1E05 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardCore;
			target = F9A1BF709B7BA4C24A83664EB1E1C7D4 /* IQKeyboardCore */;
			targetProxy = 8562EFFE3415062B1AEAE8D2A4022ACC /* PBXContainerItemProxy */;
		};
		A1C36DEFAE03A079BEBC2F8C2868CA07 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQTextInputViewNotification;
			target = 283C1F2EA88CD4413165801A6748A48E /* IQTextInputViewNotification */;
			targetProxy = 53E5E040779580711739A36432BA54F6 /* PBXContainerItemProxy */;
		};
		AF26DF1BF273C7D321F8271B3C7682FD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardToolbar;
			target = F4FE17428FD0E607723A44F17231B7A1 /* IQKeyboardToolbar */;
			targetProxy = 2D30C1FED45F709B3F9DAA0BE1C7CC67 /* PBXContainerItemProxy */;
		};
		BCD149D95B63F6C1728E21E813EE69C0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardToolbarManager-IQKeyboardToolbarManager";
			target = 7C5613175BBC4BF67E36DB4FBEBC01D0 /* IQKeyboardToolbarManager-IQKeyboardToolbarManager */;
			targetProxy = 207D8392B352577B44CF2D60EA2781C0 /* PBXContainerItemProxy */;
		};
		C0B7584C660FE9B60FDF9CC3C8F5F7D7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardManagerSwift;
			target = B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */;
			targetProxy = 6C9149577EFE2637E2388CE4CABF6727 /* PBXContainerItemProxy */;
		};
		C520E6C9D1D62E28FD6FC3B41A6BBFA5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardCore-IQKeyboardCore";
			target = EEE261386011CDF271BE289F73FF5959 /* IQKeyboardCore-IQKeyboardCore */;
			targetProxy = 420DE71FB542648339F5C040FE16DE8E /* PBXContainerItemProxy */;
		};
		CD7CA76E2E954D03A800EE45ECFBCF05 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 3AF9157132618D4DBDE61849E76A4BC6 /* PBXContainerItemProxy */;
		};
		CEABB35AE65DED58953358035B5149FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQTextInputViewNotification-IQTextInputViewNotification";
			target = 4502C7427440BEB17A50C0BF6E638A85 /* IQTextInputViewNotification-IQTextInputViewNotification */;
			targetProxy = D1F1F8EC20024CB3947D3AA2D7FBA500 /* PBXContainerItemProxy */;
		};
		DA0FEC58B6D01766C8D7E548B77A74F1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQTextView-IQTextView";
			target = A6602BCAA6F4F932A586C41D0B7E019C /* IQTextView-IQTextView */;
			targetProxy = A25F8409D0E6A62A09234894DD4BFB09 /* PBXContainerItemProxy */;
		};
		DDD7D2917FBBE628CC777A7D400D23D8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardReturnManager;
			target = 0981F89DB5DA3FFCFFEBDE1F56287054 /* IQKeyboardReturnManager */;
			targetProxy = 8CEDAB49EEB225938A1013164FA8A8A7 /* PBXContainerItemProxy */;
		};
		DDE94D20A1A6C0DD3B7DBFBAB8809B95 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardToolbar-IQKeyboardToolbar";
			target = B247F77A0CD5E19C8187A9BA1EB58C09 /* IQKeyboardToolbar-IQKeyboardToolbar */;
			targetProxy = 58693826CB37223909682D6B7EFA4BF8 /* PBXContainerItemProxy */;
		};
		DF8A0A736C48AA317DA2B3943599BE0A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardToolbarManager;
			target = 020993F16DA5986DACE118349EBCE9E5 /* IQKeyboardToolbarManager */;
			targetProxy = 46B62EF4643E8BAC81E143B61ECFD0F2 /* PBXContainerItemProxy */;
		};
		E095D2AC38047A65E57C093380AF4FC7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardNotification-IQKeyboardNotification";
			target = 2B8FF445A5162845FAB9EC00FC92B694 /* IQKeyboardNotification-IQKeyboardNotification */;
			targetProxy = 806A669B36A79529A5F494E1220F1E35 /* PBXContainerItemProxy */;
		};
		F10D1E3704249119E6845F7F2D875B9C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardReturnManager;
			target = 0981F89DB5DA3FFCFFEBDE1F56287054 /* IQKeyboardReturnManager */;
			targetProxy = 5BB96FA6BE13AA01EEDF69BC83B7920B /* PBXContainerItemProxy */;
		};
		FA82F8E10B7DBC2B3888E6A2F90C4300 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardToolbar;
			target = F4FE17428FD0E607723A44F17231B7A1 /* IQKeyboardToolbar */;
			targetProxy = ADCD21E384E7BE7E4F7BFBD8D4892030 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00077FF97D4D7BF9DBBBC523CC500657 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1D4C859E7799D93FC39352806DF3050C /* IQTextInputViewNotification.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQTextInputViewNotification";
				IBSC_MODULE = IQTextInputViewNotification;
				INFOPLIST_FILE = "Target Support Files/IQTextInputViewNotification/ResourceBundle-IQTextInputViewNotification-IQTextInputViewNotification-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQTextInputViewNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		029CECC5A41B7EDFA54117AB299B2AC7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D07634E5CD15C4026C8D7B5AC5BE071A /* IQKeyboardToolbar.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardToolbar";
				IBSC_MODULE = IQKeyboardToolbar;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbar/ResourceBundle-IQKeyboardToolbar-IQKeyboardToolbar-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardToolbar;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		05B95147B0CA1C809C22DDFDE5755A33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8EF0B2C861CC00208F8093B66920E0CB /* IQTextView.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQTextView/IQTextView-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQTextView/IQTextView-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQTextView/IQTextView.modulemap";
				PRODUCT_MODULE_NAME = IQTextView;
				PRODUCT_NAME = IQTextView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		26C2FF0585DFF7F10439C25F95DD1A2F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7CC0E4A354F42DA837785C0F3C6CC1C1 /* IQKeyboardReturnManager.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardReturnManager";
				IBSC_MODULE = IQKeyboardReturnManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardReturnManager/ResourceBundle-IQKeyboardReturnManager-IQKeyboardReturnManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardReturnManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		2AF942742426A5746F0E16D1A5DF6441 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C103B42D6A9B4F8C0D22C6167B4B35A1 /* IQTextView.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQTextView";
				IBSC_MODULE = IQTextView;
				INFOPLIST_FILE = "Target Support Files/IQTextView/ResourceBundle-IQTextView-IQTextView-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQTextView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		2C4EBC8097D4D07427A18EC0F79738BE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69203C33CD2CFF7682CD51E7B3FC9681 /* IQKeyboardManagerSwift.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManagerSwift;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		305AC6224F1B18B2BE1099B0D11B0169 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C1A405690A3BADC0EC66D4878DCBDE78 /* IQKeyboardToolbar.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardToolbar";
				IBSC_MODULE = IQKeyboardToolbar;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbar/ResourceBundle-IQKeyboardToolbar-IQKeyboardToolbar-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardToolbar;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		571967A765D0C891DB6F72E22ADED9E8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C103B42D6A9B4F8C0D22C6167B4B35A1 /* IQTextView.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQTextView/IQTextView-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQTextView/IQTextView-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQTextView/IQTextView.modulemap";
				PRODUCT_MODULE_NAME = IQTextView;
				PRODUCT_NAME = IQTextView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		5A8CF0A28096F31D9550F14C39A229A9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 25EB04025FF9AF24D2BA7911ED7F254A /* IQKeyboardManagerSwift.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManagerSwift";
				IBSC_MODULE = IQKeyboardManagerSwift;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		5B72504FF3042A4DB382993F0CA9F7A7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3F6FFB4432E607EEA8C53ACCBCEBE58C /* IQKeyboardToolbarManager.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardToolbarManager";
				IBSC_MODULE = IQKeyboardToolbarManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbarManager/ResourceBundle-IQKeyboardToolbarManager-IQKeyboardToolbarManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardToolbarManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6BCAB4C28A18C4F8B67063186B97A8DA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7CC0E4A354F42DA837785C0F3C6CC1C1 /* IQKeyboardReturnManager.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardReturnManager;
				PRODUCT_NAME = IQKeyboardReturnManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		71AF49F02DDC2E33A73B457F44A01C73 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7F7B660C82F68382BA2E6CDC1025E768 /* Pods-PanHeatLog.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		743AF9D52B2EF96B1AEABF0F12CB957D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69203C33CD2CFF7682CD51E7B3FC9681 /* IQKeyboardManagerSwift.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManagerSwift";
				IBSC_MODULE = IQKeyboardManagerSwift;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		74434B7DC7D8F3AD7E63446B51F40DD1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA4A813BF5DB47478696D10E21404434 /* IQKeyboardNotification.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardNotification;
				PRODUCT_NAME = IQKeyboardNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		851C413A7DC2A90D5249B1DD14B98BD3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D07634E5CD15C4026C8D7B5AC5BE071A /* IQKeyboardToolbar.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardToolbar;
				PRODUCT_NAME = IQKeyboardToolbar;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		8AB96A0627FB750C500B97F11771E62C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3F6FFB4432E607EEA8C53ACCBCEBE58C /* IQKeyboardToolbarManager.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardToolbarManager;
				PRODUCT_NAME = IQKeyboardToolbarManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		8FBE148785CFBA919ACD338E3C6C543F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F9450613277EED51A0DE3A784EF4A0D3 /* IQTextInputViewNotification.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification.modulemap";
				PRODUCT_MODULE_NAME = IQTextInputViewNotification;
				PRODUCT_NAME = IQTextInputViewNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		909918814E6E765E5E38D0FB6994F9B8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 612C5E6191A78693DBD595DBB136A790 /* IQKeyboardToolbarManager.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardToolbarManager/IQKeyboardToolbarManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardToolbarManager;
				PRODUCT_NAME = IQKeyboardToolbarManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		90D4D09BCB6A4660E43ACBE9ECB6FE9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		94E7C2E5F1BD8C6073113B8D3C5E1AA1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 50DF48E3B8BC217CE8D63EFA9E6B66E2 /* IQKeyboardReturnManager.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardReturnManager";
				IBSC_MODULE = IQKeyboardReturnManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardReturnManager/ResourceBundle-IQKeyboardReturnManager-IQKeyboardReturnManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardReturnManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		954F022264495C7F7E03AC18B31D38C2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B15A7CEA361315CC78E49A788C43A09B /* SnapKit.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		9553C89E183877A5CB2F3C6801BEC129 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		9927ADBDF7C5DA28F80C78EA607EE232 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 50DF48E3B8BC217CE8D63EFA9E6B66E2 /* IQKeyboardReturnManager.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardReturnManager/IQKeyboardReturnManager.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardReturnManager;
				PRODUCT_NAME = IQKeyboardReturnManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		996C820D6CC4AF5FAAC1D10F85C7775C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8EF0B2C861CC00208F8093B66920E0CB /* IQTextView.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQTextView";
				IBSC_MODULE = IQTextView;
				INFOPLIST_FILE = "Target Support Files/IQTextView/ResourceBundle-IQTextView-IQTextView-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQTextView;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FFEC1C2CF15F9BCC07A92DE61FDBBE85 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A0A3678230B9EA510F082AF1CF277AC8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 64A3274FD5C1E28B3DB0D6054718964A /* AAInfographics.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AAInfographics/AAInfographics-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AAInfographics/AAInfographics-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AAInfographics/AAInfographics.modulemap";
				PRODUCT_MODULE_NAME = AAInfographics;
				PRODUCT_NAME = AAInfographics;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A0F0EEB28C54A3D861769B31AB6A09BC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5125BBAA8DBB347FE5B8A5C95269F38C /* IQKeyboardCore.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardCore/IQKeyboardCore-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardCore/IQKeyboardCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardCore/IQKeyboardCore.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardCore;
				PRODUCT_NAME = IQKeyboardCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B95727A8106076368F1C20CFC2F0FD5B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1B2DAA31000D78F8C0672BCE1F88EC38 /* IQKeyboardCore.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardCore";
				IBSC_MODULE = IQKeyboardCore;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardCore/ResourceBundle-IQKeyboardCore-IQKeyboardCore-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		BAD20131EC29650C6737E66854A3A9FD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B15A7CEA361315CC78E49A788C43A09B /* SnapKit.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C1992231489CBEF8507D8C483CC97833 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 74DFB4BCAAEE60F0E62E35E3C000585A /* IQKeyboardNotification.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardNotification/IQKeyboardNotification.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardNotification;
				PRODUCT_NAME = IQKeyboardNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C4BE8548A73AC740FF5FC68940D86BAE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C1A405690A3BADC0EC66D4878DCBDE78 /* IQKeyboardToolbar.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardToolbar/IQKeyboardToolbar.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardToolbar;
				PRODUCT_NAME = IQKeyboardToolbar;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C8BBDEFEE07049BCED18BC1FAF1AC182 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 612C5E6191A78693DBD595DBB136A790 /* IQKeyboardToolbarManager.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardToolbarManager";
				IBSC_MODULE = IQKeyboardToolbarManager;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardToolbarManager/ResourceBundle-IQKeyboardToolbarManager-IQKeyboardToolbarManager-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardToolbarManager;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		D0C03895DE80B8549E8C1D167D22E44D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 25EB04025FF9AF24D2BA7911ED7F254A /* IQKeyboardManagerSwift.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManagerSwift;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D304DF047F32CCC16229FDBD0DAB45DB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1D4C859E7799D93FC39352806DF3050C /* IQTextInputViewNotification.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQTextInputViewNotification/IQTextInputViewNotification.modulemap";
				PRODUCT_MODULE_NAME = IQTextInputViewNotification;
				PRODUCT_NAME = IQTextInputViewNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DC0B5D8F7D34D4201575048A8893CFDB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA4A813BF5DB47478696D10E21404434 /* IQKeyboardNotification.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardNotification";
				IBSC_MODULE = IQKeyboardNotification;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardNotification/ResourceBundle-IQKeyboardNotification-IQKeyboardNotification-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		E97C5DC67C913BB6A1DFC1DE338BED75 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FFEC1C2CF15F9BCC07A92DE61FDBBE85 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		F43E79A2FA08ECFA68C501315413DC06 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB1E296D1776DA3B5740A303C07BA352 /* AAInfographics.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AAInfographics/AAInfographics-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AAInfographics/AAInfographics-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AAInfographics/AAInfographics.modulemap";
				PRODUCT_MODULE_NAME = AAInfographics;
				PRODUCT_NAME = AAInfographics;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F66A0A02FB2E178E0AF657644280904D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B69B12F4F85CCE80420D350C3A0C2B42 /* Pods-PanHeatLog.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-PanHeatLog/Pods-PanHeatLog.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F69749E837C2AF36C01281C18062C8C8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5125BBAA8DBB347FE5B8A5C95269F38C /* IQKeyboardCore.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardCore";
				IBSC_MODULE = IQKeyboardCore;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardCore/ResourceBundle-IQKeyboardCore-IQKeyboardCore-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		F858CAEEFB0B3C5FFDAD1374DE776509 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F9450613277EED51A0DE3A784EF4A0D3 /* IQTextInputViewNotification.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQTextInputViewNotification";
				IBSC_MODULE = IQTextInputViewNotification;
				INFOPLIST_FILE = "Target Support Files/IQTextInputViewNotification/ResourceBundle-IQTextInputViewNotification-IQTextInputViewNotification-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQTextInputViewNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		FC7C6D8077106D49393BD2C0809C2CF2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 74DFB4BCAAEE60F0E62E35E3C000585A /* IQKeyboardNotification.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardNotification";
				IBSC_MODULE = IQKeyboardNotification;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardNotification/ResourceBundle-IQKeyboardNotification-IQKeyboardNotification-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardNotification;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		FC91939289B6972DE0D1E8C4EDF5A399 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1B2DAA31000D78F8C0672BCE1F88EC38 /* IQKeyboardCore.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardCore/IQKeyboardCore-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardCore/IQKeyboardCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardCore/IQKeyboardCore.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardCore;
				PRODUCT_NAME = IQKeyboardCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		056B8246B82F6961CE9AB8AE61E6F06D /* Build configuration list for PBXNativeTarget "IQKeyboardToolbar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4BE8548A73AC740FF5FC68940D86BAE /* Debug */,
				851C413A7DC2A90D5249B1DD14B98BD3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0A3763672A15AC97F3BA7516D6705609 /* Build configuration list for PBXNativeTarget "IQKeyboardToolbar-IQKeyboardToolbar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				305AC6224F1B18B2BE1099B0D11B0169 /* Debug */,
				029CECC5A41B7EDFA54117AB299B2AC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2B0ADC8A5D956F58C0F317AF3DCD7B6A /* Build configuration list for PBXNativeTarget "AAInfographics" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F43E79A2FA08ECFA68C501315413DC06 /* Debug */,
				A0A3678230B9EA510F082AF1CF277AC8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2FF989C844B9779E4D252C946FAE2517 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift-IQKeyboardManagerSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5A8CF0A28096F31D9550F14C39A229A9 /* Debug */,
				743AF9D52B2EF96B1AEABF0F12CB957D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3208C0F97FCB948C4F12E81E25FFEAF9 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D0C03895DE80B8549E8C1D167D22E44D /* Debug */,
				2C4EBC8097D4D07427A18EC0F79738BE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				90D4D09BCB6A4660E43ACBE9ECB6FE9A /* Debug */,
				9553C89E183877A5CB2F3C6801BEC129 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		50002D22DCFE9A2A2D579EC37E1ABC28 /* Build configuration list for PBXNativeTarget "Pods-PanHeatLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				71AF49F02DDC2E33A73B457F44A01C73 /* Debug */,
				F66A0A02FB2E178E0AF657644280904D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5B957002C814061E6565BAE4FD99792F /* Build configuration list for PBXNativeTarget "IQKeyboardNotification-IQKeyboardNotification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC7C6D8077106D49393BD2C0809C2CF2 /* Debug */,
				DC0B5D8F7D34D4201575048A8893CFDB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5F0B3B2A93E52C770D727217C1EC6443 /* Build configuration list for PBXNativeTarget "IQKeyboardReturnManager-IQKeyboardReturnManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				94E7C2E5F1BD8C6073113B8D3C5E1AA1 /* Debug */,
				26C2FF0585DFF7F10439C25F95DD1A2F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6BB3220D2825BD6888456AE177C7DBC2 /* Build configuration list for PBXNativeTarget "IQKeyboardToolbarManager-IQKeyboardToolbarManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5B72504FF3042A4DB382993F0CA9F7A7 /* Debug */,
				C8BBDEFEE07049BCED18BC1FAF1AC182 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6E50B5B04FE20028F9E8935EA9CC5DFC /* Build configuration list for PBXNativeTarget "IQKeyboardNotification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C1992231489CBEF8507D8C483CC97833 /* Debug */,
				74434B7DC7D8F3AD7E63446B51F40DD1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		760610724531E6588FC50CE6A17DADEB /* Build configuration list for PBXNativeTarget "IQKeyboardToolbarManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8AB96A0627FB750C500B97F11771E62C /* Debug */,
				909918814E6E765E5E38D0FB6994F9B8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */,
				BAD20131EC29650C6737E66854A3A9FD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E12284EF50ABF442B8D88AC686108E1 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E97C5DC67C913BB6A1DFC1DE338BED75 /* Debug */,
				954F022264495C7F7E03AC18B31D38C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BB7046F2A56F7CB32A0EA10728C363C /* Build configuration list for PBXNativeTarget "IQTextView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				571967A765D0C891DB6F72E22ADED9E8 /* Debug */,
				05B95147B0CA1C809C22DDFDE5755A33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF179C10572D282D3F2E796E6329C713 /* Build configuration list for PBXNativeTarget "IQKeyboardReturnManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9927ADBDF7C5DA28F80C78EA607EE232 /* Debug */,
				6BCAB4C28A18C4F8B67063186B97A8DA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B2130418C2FAD0A1E2FD872758AD609B /* Build configuration list for PBXNativeTarget "IQKeyboardCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A0F0EEB28C54A3D861769B31AB6A09BC /* Debug */,
				FC91939289B6972DE0D1E8C4EDF5A399 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BB26072735112C4E5E4EF5DFD0C82653 /* Build configuration list for PBXNativeTarget "IQTextInputViewNotification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8FBE148785CFBA919ACD338E3C6C543F /* Debug */,
				D304DF047F32CCC16229FDBD0DAB45DB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C66EEF1417828B16F28463A73C17310A /* Build configuration list for PBXNativeTarget "IQTextView-IQTextView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2AF942742426A5746F0E16D1A5DF6441 /* Debug */,
				996C820D6CC4AF5FAAC1D10F85C7775C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9EFE37ED5928B072A638BAEECD54E19 /* Build configuration list for PBXNativeTarget "IQTextInputViewNotification-IQTextInputViewNotification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F858CAEEFB0B3C5FFDAD1374DE776509 /* Debug */,
				00077FF97D4D7BF9DBBBC523CC500657 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D8E48C460FD2B2F83D9E530EB0177D13 /* Build configuration list for PBXNativeTarget "IQKeyboardCore-IQKeyboardCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F69749E837C2AF36C01281C18062C8C8 /* Debug */,
				B95727A8106076368F1C20CFC2F0FD5B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
