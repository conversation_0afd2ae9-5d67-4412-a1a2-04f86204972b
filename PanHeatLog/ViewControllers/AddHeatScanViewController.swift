//
//  AddHeatScanViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class AddHeatScanViewController: UIViewController {

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()

    private let titleField = BaseTextField()
    private var panTypeSegment = UISegmentedControl()
    private var heatSourceSegment = UISegmentedControl()

    private let initialTempField = BaseTextField()
    private let targetTempField = BaseTextField()
    private let heatingTimeField = BaseTextField()

    private var heatDistributionSegment = UISegmentedControl()
    private let efficiencySlider = UISlider()
    private let efficiencyLabel = UILabel()
    private let consistencySlider = UISlider()
    private let consistencyLabel = UILabel()

    private let centerHotSpotSwitch = UISwitch()
    private let edgeHotSpotSwitch = UISwitch()
    private let burnSpotsSwitch = UISwitch()
    private let unevenCookingSwitch = UISwitch()
    private let cookingSuccessSwitch = UISwitch()

    private let notesTextView = UITextView()
    private let saveButton = BaseButton(style: .primary, size: .large)

    // MARK: - Properties
    private let dataManager = HeatScanDataManager.shared

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        setupFormFields()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "Add Heat Scan"

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        setupFormComponents()
        setupConstraints()
    }

    private func setupNavigationBar() {
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
    }

    private func setupFormComponents() {
        // Title field
        titleField.title = "Record Title"
        titleField.placeholder = "Enter a descriptive title"
        titleField.isRequired = true

        // Pan type segment
        let panTypes = PanType.allCases.prefix(4)
        panTypeSegment = UISegmentedControl(items: panTypes.map { $0.displayName })
        panTypeSegment.selectedSegmentIndex = 0

        // Heat source segment
        let heatSources = HeatSource.allCases.prefix(4)
        heatSourceSegment = UISegmentedControl(items: heatSources.map { $0.displayName })
        heatSourceSegment.selectedSegmentIndex = 0

        // Temperature fields
        initialTempField.title = "Initial Temperature (°C)"
        initialTempField.placeholder = "20"
        initialTempField.keyboardType = .decimalPad
        initialTempField.isRequired = true

        targetTempField.title = "Target Temperature (°C)"
        targetTempField.placeholder = "180"
        targetTempField.keyboardType = .decimalPad
        targetTempField.isRequired = true

        heatingTimeField.title = "Heating Time (seconds)"
        heatingTimeField.placeholder = "240"
        heatingTimeField.keyboardType = .numberPad
        heatingTimeField.isRequired = true

        // Heat distribution
        let distributions = HeatDistribution.allCases
        heatDistributionSegment = UISegmentedControl(items: distributions.map { $0.displayName })
        heatDistributionSegment.selectedSegmentIndex = 2 // Good

        // Sliders
        setupSliders()

        // Switches
        setupSwitches()

        // Notes
        setupNotesTextView()

        // Save button
        saveButton.setTitle("Save Heat Scan Record", for: .normal)
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)

        [titleField, panTypeSegment, heatSourceSegment, initialTempField, targetTempField,
         heatingTimeField, heatDistributionSegment, efficiencySlider, efficiencyLabel,
         consistencySlider, consistencyLabel, centerHotSpotSwitch, edgeHotSpotSwitch,
         burnSpotsSwitch, unevenCookingSwitch, cookingSuccessSwitch, notesTextView, saveButton].forEach {
            contentView.addSubview($0)
        }
    }

    private func setupSliders() {
        efficiencySlider.minimumValue = 1.0
        efficiencySlider.maximumValue = 5.0
        efficiencySlider.value = 3.0
        efficiencySlider.addTarget(self, action: #selector(efficiencyChanged), for: .valueChanged)

        efficiencyLabel.text = "Heating Efficiency: 3.0"
        efficiencyLabel.font = AppTheme.Typography.subheadline
        efficiencyLabel.textColor = AppTheme.Colors.primaryText

        consistencySlider.minimumValue = 1.0
        consistencySlider.maximumValue = 5.0
        consistencySlider.value = 3.0
        consistencySlider.addTarget(self, action: #selector(consistencyChanged), for: .valueChanged)

        consistencyLabel.text = "Temperature Consistency: 3.0"
        consistencyLabel.font = AppTheme.Typography.subheadline
        consistencyLabel.textColor = AppTheme.Colors.primaryText
    }

    private func setupSwitches() {
        centerHotSpotSwitch.isOn = false
        edgeHotSpotSwitch.isOn = false
        burnSpotsSwitch.isOn = false
        unevenCookingSwitch.isOn = false
        cookingSuccessSwitch.isOn = true
    }

    private func setupNotesTextView() {
        notesTextView.font = AppTheme.Typography.body
        notesTextView.textColor = AppTheme.Colors.primaryText
        notesTextView.backgroundColor = AppTheme.Colors.background
        notesTextView.layer.cornerRadius = AppTheme.CornerRadius.small
        notesTextView.layer.borderWidth = 1
        notesTextView.layer.borderColor = AppTheme.Colors.border.cgColor
        notesTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
    }

    private func setupFormFields() {
        titleField.delegate = self
        initialTempField.delegate = self
        targetTempField.delegate = self
        heatingTimeField.delegate = self
    }

    private func setupConstraints() {
        var lastView: UIView = contentView
        let spacing = AppTheme.Spacing.md

        // Title field
        titleField.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = titleField

        // Pan type section
        let panTypeLabel = createSectionLabel("Pan Type")
        contentView.addSubview(panTypeLabel)
        panTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        panTypeSegment.snp.makeConstraints { make in
            make.top.equalTo(panTypeLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = panTypeSegment

        // Heat source section
        let heatSourceLabel = createSectionLabel("Heat Source")
        contentView.addSubview(heatSourceLabel)
        heatSourceLabel.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        heatSourceSegment.snp.makeConstraints { make in
            make.top.equalTo(heatSourceLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = heatSourceSegment

        // Temperature fields
        initialTempField.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        targetTempField.snp.makeConstraints { make in
            make.top.equalTo(initialTempField.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        heatingTimeField.snp.makeConstraints { make in
            make.top.equalTo(targetTempField.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = heatingTimeField

        // Heat distribution section
        let distributionLabel = createSectionLabel("Heat Distribution")
        contentView.addSubview(distributionLabel)
        distributionLabel.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        heatDistributionSegment.snp.makeConstraints { make in
            make.top.equalTo(distributionLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        // Efficiency slider
        efficiencyLabel.snp.makeConstraints { make in
            make.top.equalTo(heatDistributionSegment.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        efficiencySlider.snp.makeConstraints { make in
            make.top.equalTo(efficiencyLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        // Consistency slider
        consistencyLabel.snp.makeConstraints { make in
            make.top.equalTo(efficiencySlider.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        consistencySlider.snp.makeConstraints { make in
            make.top.equalTo(consistencyLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        // Switches section
        let switchesLabel = createSectionLabel("Cooking Results")
        contentView.addSubview(switchesLabel)
        switchesLabel.snp.makeConstraints { make in
            make.top.equalTo(consistencySlider.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        let switchStack = createSwitchStack()
        contentView.addSubview(switchStack)
        switchStack.snp.makeConstraints { make in
            make.top.equalTo(switchesLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        // Notes section
        let notesLabel = createSectionLabel("Notes")
        contentView.addSubview(notesLabel)
        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(switchStack.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }

        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(notesLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(spacing)
            make.height.equalTo(100)
        }

        // Save button
        saveButton.snp.makeConstraints { make in
            make.top.equalTo(notesTextView.snp.bottom).offset(spacing * 2)
            make.leading.trailing.equalToSuperview().inset(spacing)
            make.bottom.equalToSuperview().offset(-spacing)
        }
    }

    private func createSectionLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = AppTheme.Typography.headline
        label.textColor = AppTheme.Colors.primaryText
        return label
    }

    private func createSwitchStack() -> UIStackView {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = AppTheme.Spacing.md

        let switches = [
            ("Center Hot Spot", centerHotSpotSwitch),
            ("Edge Hot Spot", edgeHotSpotSwitch),
            ("Burn Spots", burnSpotsSwitch),
            ("Uneven Cooking", unevenCookingSwitch),
            ("Cooking Success", cookingSuccessSwitch)
        ]

        for (title, switchControl) in switches {
            let container = UIView()
            let label = UILabel()
            label.text = title
            label.font = AppTheme.Typography.body
            label.textColor = AppTheme.Colors.primaryText

            container.addSubview(label)
            container.addSubview(switchControl)

            label.snp.makeConstraints { make in
                make.leading.centerY.equalToSuperview()
            }

            switchControl.snp.makeConstraints { make in
                make.trailing.centerY.equalToSuperview()
                make.top.bottom.equalToSuperview()
            }

            stack.addArrangedSubview(container)
        }

        return stack
    }

    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }

    @objc private func efficiencyChanged() {
        efficiencyLabel.text = String(format: "Heating Efficiency: %.1f", efficiencySlider.value)
    }

    @objc private func consistencyChanged() {
        consistencyLabel.text = String(format: "Temperature Consistency: %.1f", consistencySlider.value)
    }

    @objc private func saveButtonTapped() {
        guard validateForm() else { return }

        let record = createHeatScanRecord()
        dataManager.saveRecord(record)

        let alert = UIAlertController(title: "Success", message: "Heat scan record saved successfully!", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
            self?.dismiss(animated: true)
        })
        present(alert, animated: true)
    }

    private func validateForm() -> Bool {
        var isValid = true

        if !titleField.validate() { isValid = false }
        if !initialTempField.validate() { isValid = false }
        if !targetTempField.validate() { isValid = false }
        if !heatingTimeField.validate() { isValid = false }

        return isValid
    }

    private func createHeatScanRecord() -> HeatScanRecord {
        let panTypes = Array(PanType.allCases.prefix(4))
        let heatSources = Array(HeatSource.allCases.prefix(4))
        let distributions = HeatDistribution.allCases

        return HeatScanRecord(
            title: titleField.text ?? "",
            panType: panTypes[panTypeSegment.selectedSegmentIndex],
            heatSource: heatSources[heatSourceSegment.selectedSegmentIndex],
            initialTemperature: Double(initialTempField.text ?? "0") ?? 0,
            targetTemperature: Double(targetTempField.text ?? "0") ?? 0,
            heatingTime: Double(heatingTimeField.text ?? "0") ?? 0,
            heatDistribution: distributions[heatDistributionSegment.selectedSegmentIndex],
            centerHotSpot: centerHotSpotSwitch.isOn,
            edgeHotSpot: edgeHotSpotSwitch.isOn,
            heatingEfficiency: Double(efficiencySlider.value),
            temperatureConsistency: Double(consistencySlider.value),
            burnSpots: burnSpotsSwitch.isOn,
            unevenCooking: unevenCookingSwitch.isOn,
            cookingSuccess: cookingSuccessSwitch.isOn,
            notes: notesTextView.text.isEmpty ? nil : notesTextView.text
        )
    }
}

// MARK: - UITextFieldDelegate
extension AddHeatScanViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }
}
