//
//  AddHeatScanViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class AddHeatScanViewController: UIViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "Add Heat Scan"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        // Temporary label
        let label = UILabel()
        label.text = "Add Heat Scan Form\n(Coming Soon)"
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = AppTheme.Typography.title2
        label.textColor = AppTheme.Colors.secondaryText
        
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
}
