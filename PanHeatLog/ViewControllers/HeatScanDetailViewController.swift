//
//  HeatScanDetailViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class HeatScanDetailViewController: UIViewController {

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let headerView = UIView()
    private let titleLabel = UILabel()
    private let dateLabel = UILabel()
    private let ratingView = UIView()
    private let ratingLabel = UILabel()

    // MARK: - Properties
    private let record: HeatScanRecord

    init(record: HeatScanRecord) {
        self.record = record
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        populateData()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "Heat Scan Details"

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        setupHeader()
        setupDetailSections()
    }

    private func setupHeader() {
        headerView.applyCardStyle()
        contentView.addSubview(headerView)

        titleLabel.font = AppTheme.Typography.title1
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.numberOfLines = 0

        dateLabel.font = AppTheme.Typography.subheadline
        dateLabel.textColor = AppTheme.Colors.secondaryText

        ratingView.backgroundColor = AppTheme.Colors.accent
        ratingView.layer.cornerRadius = 20

        ratingLabel.font = UIFont.systemFont(ofSize: 17, weight: .bold)
        ratingLabel.textColor = .white
        ratingLabel.textAlignment = .center

        [titleLabel, dateLabel, ratingView].forEach { headerView.addSubview($0) }
        ratingView.addSubview(ratingLabel)

        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(AppTheme.Spacing.md)
            make.trailing.equalTo(ratingView.snp.leading).offset(-AppTheme.Spacing.md)
        }

        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.xs)
            make.leading.equalTo(titleLabel)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.md)
        }

        ratingView.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.width.height.equalTo(40)
        }

        ratingLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    private func setupDetailSections() {
        var lastView: UIView = headerView
        let spacing = AppTheme.Spacing.md

        // Basic Info Section
        let basicInfoCard = createInfoCard(title: "Basic Information", items: [
            ("Pan Type", "\(record.panType.icon) \(record.panType.displayName)"),
            ("Heat Source", "\(record.heatSource.icon) \(record.heatSource.displayName)"),
            ("Initial Temperature", String(format: "%.1f°C", record.initialTemperature)),
            ("Target Temperature", String(format: "%.1f°C", record.targetTemperature)),
            ("Heating Time", record.formattedHeatingTime)
        ])

        contentView.addSubview(basicInfoCard)
        basicInfoCard.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = basicInfoCard

        // Performance Section
        let performanceCard = createInfoCard(title: "Performance Metrics", items: [
            ("Heat Distribution", record.heatDistribution.displayName),
            ("Heating Efficiency", String(format: "%.1f/5.0", record.heatingEfficiency)),
            ("Temperature Consistency", String(format: "%.1f/5.0", record.temperatureConsistency)),
            ("Overall Rating", String(format: "%.1f/5.0", record.overallRating))
        ])

        contentView.addSubview(performanceCard)
        performanceCard.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = performanceCard

        // Results Section
        let resultsCard = createResultsCard()
        contentView.addSubview(resultsCard)
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(spacing)
            make.leading.trailing.equalToSuperview().inset(spacing)
        }
        lastView = resultsCard

        // Notes Section
        if let notes = record.notes, !notes.isEmpty {
            let notesCard = createNotesCard(notes: notes)
            contentView.addSubview(notesCard)
            notesCard.snp.makeConstraints { make in
                make.top.equalTo(lastView.snp.bottom).offset(spacing)
                make.leading.trailing.equalToSuperview().inset(spacing)
                make.bottom.equalToSuperview().offset(-spacing)
            }
        } else {
            lastView.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-spacing)
            }
        }
    }

    private func createInfoCard(title: String, items: [(String, String)]) -> UIView {
        let card = UIView()
        card.applyCardStyle()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.primaryText

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = AppTheme.Spacing.sm

        card.addSubview(titleLabel)
        card.addSubview(stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        for (key, value) in items {
            let itemView = createInfoItem(key: key, value: value)
            stackView.addArrangedSubview(itemView)
        }

        return card
    }

    private func createInfoItem(key: String, value: String) -> UIView {
        let container = UIView()

        let keyLabel = UILabel()
        keyLabel.text = key
        keyLabel.font = AppTheme.Typography.subheadline
        keyLabel.textColor = AppTheme.Colors.secondaryText

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = AppTheme.Typography.body
        valueLabel.textColor = AppTheme.Colors.primaryText
        valueLabel.textAlignment = .right

        container.addSubview(keyLabel)
        container.addSubview(valueLabel)

        keyLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.lessThanOrEqualTo(container.snp.width).multipliedBy(0.6)
        }

        valueLabel.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.leading.greaterThanOrEqualTo(keyLabel.snp.trailing).offset(AppTheme.Spacing.sm)
            make.top.bottom.equalToSuperview()
        }

        return container
    }

    private func createResultsCard() -> UIView {
        let card = UIView()
        card.applyCardStyle()

        let titleLabel = UILabel()
        titleLabel.text = "Cooking Results"
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.primaryText

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = AppTheme.Spacing.sm

        card.addSubview(titleLabel)
        card.addSubview(stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        let results = [
            ("Center Hot Spot", record.centerHotSpot),
            ("Edge Hot Spot", record.edgeHotSpot),
            ("Burn Spots", record.burnSpots),
            ("Uneven Cooking", record.unevenCooking),
            ("Cooking Success", record.cookingSuccess)
        ]

        for (title, value) in results {
            let itemView = createResultItem(title: title, value: value)
            stackView.addArrangedSubview(itemView)
        }

        return card
    }

    private func createResultItem(title: String, value: Bool) -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.body
        titleLabel.textColor = AppTheme.Colors.primaryText

        let statusView = UIView()
        statusView.backgroundColor = value ? AppTheme.Colors.success : AppTheme.Colors.border
        statusView.layer.cornerRadius = 8

        let statusLabel = UILabel()
        statusLabel.text = value ? "✓" : "✗"
        statusLabel.font = UIFont.systemFont(ofSize: 12, weight: .bold)
        statusLabel.textColor = value ? .white : AppTheme.Colors.secondaryText
        statusLabel.textAlignment = .center

        container.addSubview(titleLabel)
        container.addSubview(statusView)
        statusView.addSubview(statusLabel)

        titleLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
        }

        statusView.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.width.height.equalTo(24)
            make.top.bottom.equalToSuperview()
        }

        statusLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        return container
    }

    private func createNotesCard(notes: String) -> UIView {
        let card = UIView()
        card.applyCardStyle()

        let titleLabel = UILabel()
        titleLabel.text = "Notes"
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.primaryText

        let notesLabel = UILabel()
        notesLabel.text = notes
        notesLabel.font = AppTheme.Typography.body
        notesLabel.textColor = AppTheme.Colors.primaryText
        notesLabel.numberOfLines = 0

        card.addSubview(titleLabel)
        card.addSubview(notesLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        return card
    }

    private func populateData() {
        titleLabel.text = record.title

        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        dateLabel.text = formatter.string(from: record.createdAt)

        ratingLabel.text = String(format: "%.1f", record.overallRating)

        // Update rating color based on value
        let rating = record.overallRating
        if rating >= 4.0 {
            ratingView.backgroundColor = AppTheme.Colors.success
        } else if rating >= 3.0 {
            ratingView.backgroundColor = AppTheme.Colors.accent
        } else if rating >= 2.0 {
            ratingView.backgroundColor = AppTheme.Colors.warning
        } else {
            ratingView.backgroundColor = AppTheme.Colors.error
        }
    }
}
