//
//  HeatScanDetailViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class HeatScanDetailViewController: UIViewController {
    
    private let record: HeatScanRecord
    
    init(record: HeatScanRecord) {
        self.record = record
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = record.title
        
        // Temporary label
        let label = UILabel()
        label.text = "Heat Scan Detail View\n(Coming Soon)\n\nRecord: \(record.title)"
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = AppTheme.Typography.title2
        label.textColor = AppTheme.Colors.secondaryText
        
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
}
