//
//  HeatScanListViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class HeatScanListViewController: UIViewController {

    // MARK: - UI Components

    private let searchController = UISearchController(searchResultsController: nil)
    private let tableView = UITableView()
    private let emptyStateView = UIView()
    private let emptyStateLabel = UILabel()
    private let addButton = BaseButton(style: .primary, size: .large)
    private let filterButton = UIBarButtonItem()

    // MARK: - Properties

    private let dataManager = HeatScanDataManager.shared
    private var allRecords: [HeatScanRecord] = []
    private var filteredRecords: [HeatScanRecord] = []
    private var currentFilter: FilterOptions = FilterOptions()

    private var isSearching: Bool {
        return searchController.isActive && !searchBarText.isEmpty
    }

    private var searchBarText: String {
        return searchController.searchBar.text ?? ""
    }

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupSearchController()
        setupNavigationBar()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    // MARK: - Setup

    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "Heat Scan Records"

        setupTableView()
        setupEmptyState()
        setupAddButton()
        setupConstraints()
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")

        // Add refresh control
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        tableView.refreshControl = refreshControl

        view.addSubview(tableView)
    }

    private func setupEmptyState() {
        emptyStateLabel.text = "No heat scan records yet.\nTap the + button to create your first record!"
        emptyStateLabel.textAlignment = .center
        emptyStateLabel.numberOfLines = 0
        emptyStateLabel.font = AppTheme.Typography.body
        emptyStateLabel.textColor = AppTheme.Colors.secondaryText

        emptyStateView.addSubview(emptyStateLabel)
        view.addSubview(emptyStateView)

        emptyStateView.isHidden = true
    }

    private func setupAddButton() {
        addButton.setTitle("Add Heat Scan Record", for: .normal)
        addButton.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
        view.addSubview(addButton)
    }

    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.bottom.equalToSuperview()
        }

        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.xl)
        }

        emptyStateLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }

        addButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-AppTheme.Spacing.md)
        }
    }

    private func setupSearchController() {
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search records..."
        searchController.searchBar.tintColor = AppTheme.Colors.primary

        navigationItem.searchController = searchController
        definesPresentationContext = true
    }

    private func setupNavigationBar() {
        // Filter button
        filterButton.image = UIImage(systemName: "line.3.horizontal.decrease.circle")
        filterButton.target = self
        filterButton.action = #selector(filterButtonTapped)

        // Add button
        let addBarButton = UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(addButtonTapped)
        )

        navigationItem.rightBarButtonItems = [addBarButton, filterButton]
    }

    // MARK: - Data Loading

    private func loadData() {
        allRecords = dataManager.getAllRecords()
        applyFiltersAndSearch()
    }

    private func applyFiltersAndSearch() {
        var records = allRecords

        // Apply filters
        if let panType = currentFilter.panType {
            records = records.filter { $0.panType == panType }
        }

        if let heatSource = currentFilter.heatSource {
            records = records.filter { $0.heatSource == heatSource }
        }

        if currentFilter.successfulOnly {
            records = records.filter { $0.cookingSuccess }
        }

        // Apply search
        if isSearching {
            records = records.filter { record in
                let searchText = searchBarText.lowercased()
                return record.title.lowercased().contains(searchText) ||
                       record.panType.displayName.lowercased().contains(searchText) ||
                       record.heatSource.displayName.lowercased().contains(searchText) ||
                       (record.notes?.lowercased().contains(searchText) ?? false)
            }
        }

        // Sort by date (newest first)
        records.sort { $0.createdAt > $1.createdAt }

        filteredRecords = records
        updateUI()
    }

    private func updateUI() {
        DispatchQueue.main.async {
            self.tableView.reloadData()
            self.emptyStateView.isHidden = !self.filteredRecords.isEmpty
            self.addButton.isHidden = !self.filteredRecords.isEmpty
        }
    }

    // MARK: - Actions

    @objc private func addButtonTapped() {
        let addViewController = AddHeatScanViewController()
        let navigationController = UINavigationController(rootViewController: addViewController)
        AppTheme.configureNavigationController(navigationController)
        present(navigationController, animated: true)
    }

    @objc private func filterButtonTapped() {
        // Temporary placeholder
        let alert = UIAlertController(title: "Filter", message: "Filter feature coming soon!", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func refreshData() {
        loadData()
        tableView.refreshControl?.endRefreshing()
    }
}

// MARK: - UITableViewDataSource

extension HeatScanListViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredRecords.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)

        let record = filteredRecords[indexPath.row]
        cell.textLabel?.text = record.title
        cell.detailTextLabel?.text = "\(record.panType.displayName) • \(record.heatSource.displayName)"
        cell.accessoryType = .disclosureIndicator

        return cell
    }
}

// MARK: - UITableViewDelegate

extension HeatScanListViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let record = filteredRecords[indexPath.row]
        let detailViewController = HeatScanDetailViewController(record: record)
        navigationController?.pushViewController(detailViewController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let record = filteredRecords[indexPath.row]
            showDeleteConfirmation(for: record, at: indexPath)
        }
    }

    private func showDeleteConfirmation(for record: HeatScanRecord, at indexPath: IndexPath) {
        let alert = UIAlertController(
            title: "Delete Record",
            message: "Are you sure you want to delete '\(record.title)'?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { [weak self] _ in
            self?.deleteRecord(record, at: indexPath)
        })

        present(alert, animated: true)
    }

    private func deleteRecord(_ record: HeatScanRecord, at indexPath: IndexPath) {
        dataManager.deleteRecord(withId: record.id)
        filteredRecords.remove(at: indexPath.row)

        tableView.deleteRows(at: [indexPath], with: .fade)

        if filteredRecords.isEmpty {
            updateUI()
        }
    }
}

// MARK: - UISearchResultsUpdating

extension HeatScanListViewController: UISearchResultsUpdating {

    func updateSearchResults(for searchController: UISearchController) {
        applyFiltersAndSearch()
    }
}

// MARK: - Filter Options

struct FilterOptions {
    var panType: PanType?
    var heatSource: HeatSource?
    var successfulOnly: Bool = false

    var hasActiveFilters: Bool {
        return panType != nil || heatSource != nil || successfulOnly
    }
}
