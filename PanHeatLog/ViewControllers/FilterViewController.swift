//
//  FilterViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class FilterViewController: UIViewController {
    
    private var currentFilter: FilterOptions
    private let onFilterChanged: (FilterOptions) -> Void
    
    init(currentFilter: FilterOptions, onFilterChanged: @escaping (FilterOptions) -> Void) {
        self.currentFilter = currentFilter
        self.onFilterChanged = onFilterChanged
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "Filter Records"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneTapped)
        )
        
        // Temporary label
        let label = UILabel()
        label.text = "Filter Options\n(Coming Soon)"
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = AppTheme.Typography.title2
        label.textColor = AppTheme.Colors.secondaryText
        
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func doneTapped() {
        onFilterChanged(currentFilter)
        dismiss(animated: true)
    }
}
