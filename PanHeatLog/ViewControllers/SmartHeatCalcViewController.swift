//
//  SmartHeatCalcViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

// MARK: - Data Models

struct PreheatResult {
    let recommendedTime: Double // in seconds
    let heatMode: HeatMode
    let overheatingRisk: OverheatingRisk
    let tips: [String]
}

enum HeatMode {
    case low, medium, high

    var displayName: String {
        switch self {
        case .low: return "Low Heat"
        case .medium: return "Medium Heat"
        case .high: return "High Heat"
        }
    }

    var description: String {
        switch self {
        case .low: return "Gentle warming, perfect for delicate foods"
        case .medium: return "Standard preheating for most cooking"
        case .high: return "Quick heating for searing and high-temp cooking"
        }
    }
}

enum OverheatingRisk {
    case low, medium, high

    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        }
    }
}

class SmartHeatCalcViewController: UIViewController {

    // MARK: - UI Components

    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let titleLabel = UILabel()
    private let segmentedControl = UISegmentedControl(items: ["Preheat Calculator", "Stir Rhythm"])

    // Container views for each calculator
    private let preheatContainer = UIView()
    private let stirContainer = UIView()

    // Preheat Calculator Components
    private let materialLabel = UILabel()
    private let materialSegment = UISegmentedControl(items: ["Stainless Steel", "Cast Iron", "Carbon Steel", "Aluminum"])

    private let thicknessLabel = UILabel()
    private let thicknessSlider = UISlider()
    private let thicknessValueLabel = UILabel()

    private let heatSourceLabel = UILabel()
    private let heatSourceSegment = UISegmentedControl(items: ["Gas", "Electric", "Induction"])

    private let temperatureLabel = UILabel()
    private let temperatureSlider = UISlider()
    private let temperatureValueLabel = UILabel()

    private let calculateButton = UIButton(type: .system)

    // Result display
    private let resultCard = UIView()
    private let timeResultLabel = UILabel()
    private let modeResultLabel = UILabel()
    private let riskResultLabel = UILabel()
    private let tipsStackView = UIStackView()

    // Stir Rhythm Components
    private let stirTitleLabel = UILabel()
    private let stirComingSoonLabel = UILabel()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()

        // Show preheat calculator by default
        showPreheatCalculator()
    }

    // MARK: - Setup Methods

    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "SmartHeat Calc"

        // Configure scroll view
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        // Configure title
        titleLabel.text = "Multi-Function Heat Calculator"
        titleLabel.font = AppTheme.Typography.largeTitle
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0

        // Configure segmented control
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.backgroundColor = AppTheme.Colors.surface
        segmentedControl.selectedSegmentTintColor = AppTheme.Colors.primary
        segmentedControl.setTitleTextAttributes([
            .foregroundColor: AppTheme.Colors.primaryText
        ], for: .normal)
        segmentedControl.setTitleTextAttributes([
            .foregroundColor: UIColor.white
        ], for: .selected)

        // Configure containers
        preheatContainer.backgroundColor = .clear
        stirContainer.backgroundColor = .clear
        stirContainer.isHidden = true

        setupPreheatCalculator()
        setupStirCalculator()

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(titleLabel)
        contentView.addSubview(segmentedControl)
        contentView.addSubview(preheatContainer)
        contentView.addSubview(stirContainer)
    }

    private func setupPreheatCalculator() {
        // Material selection
        materialLabel.text = "Pan Material"
        materialLabel.font = AppTheme.Typography.headline
        materialLabel.textColor = AppTheme.Colors.primaryText

        materialSegment.selectedSegmentIndex = 0
        materialSegment.backgroundColor = AppTheme.Colors.surface
        materialSegment.selectedSegmentTintColor = AppTheme.Colors.primary

        // Thickness slider
        thicknessLabel.text = "Pan Thickness"
        thicknessLabel.font = AppTheme.Typography.headline
        thicknessLabel.textColor = AppTheme.Colors.primaryText

        thicknessSlider.minimumValue = 1.0
        thicknessSlider.maximumValue = 8.0
        thicknessSlider.value = 3.0
        thicknessSlider.tintColor = AppTheme.Colors.primary

        thicknessValueLabel.text = "3.0 mm"
        thicknessValueLabel.font = AppTheme.Typography.body
        thicknessValueLabel.textColor = AppTheme.Colors.secondaryText
        thicknessValueLabel.textAlignment = .right

        // Heat source
        heatSourceLabel.text = "Heat Source"
        heatSourceLabel.font = AppTheme.Typography.headline
        heatSourceLabel.textColor = AppTheme.Colors.primaryText

        heatSourceSegment.selectedSegmentIndex = 0
        heatSourceSegment.backgroundColor = AppTheme.Colors.surface
        heatSourceSegment.selectedSegmentTintColor = AppTheme.Colors.primary

        // Temperature slider
        temperatureLabel.text = "Ambient Temperature"
        temperatureLabel.font = AppTheme.Typography.headline
        temperatureLabel.textColor = AppTheme.Colors.primaryText

        temperatureSlider.minimumValue = 15.0
        temperatureSlider.maximumValue = 35.0
        temperatureSlider.value = 22.0
        temperatureSlider.tintColor = AppTheme.Colors.primary

        temperatureValueLabel.text = "22°C"
        temperatureValueLabel.font = AppTheme.Typography.body
        temperatureValueLabel.textColor = AppTheme.Colors.secondaryText
        temperatureValueLabel.textAlignment = .right

        // Calculate button
        calculateButton.setTitle("Calculate Preheat Time", for: .normal)
        calculateButton.titleLabel?.font = AppTheme.Typography.headline
        calculateButton.backgroundColor = AppTheme.Colors.primary
        calculateButton.setTitleColor(.white, for: .normal)
        calculateButton.layer.cornerRadius = 12

        // Result card
        resultCard.backgroundColor = AppTheme.Colors.surface
        resultCard.layer.cornerRadius = 16
        resultCard.layer.shadowColor = UIColor.black.cgColor
        resultCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        resultCard.layer.shadowRadius = 8
        resultCard.layer.shadowOpacity = 0.1
        resultCard.isHidden = true

        timeResultLabel.font = AppTheme.Typography.title2
        timeResultLabel.textColor = AppTheme.Colors.primaryText
        timeResultLabel.textAlignment = .center
        timeResultLabel.numberOfLines = 0

        modeResultLabel.font = AppTheme.Typography.body
        modeResultLabel.textColor = AppTheme.Colors.secondaryText
        modeResultLabel.textAlignment = .center
        modeResultLabel.numberOfLines = 0

        riskResultLabel.font = AppTheme.Typography.headline
        riskResultLabel.textAlignment = .center
        riskResultLabel.numberOfLines = 0

        tipsStackView.axis = .vertical
        tipsStackView.spacing = AppTheme.Spacing.sm
        tipsStackView.alignment = .leading

        // Add preheat components to container
        preheatContainer.addSubview(materialLabel)
        preheatContainer.addSubview(materialSegment)
        preheatContainer.addSubview(thicknessLabel)
        preheatContainer.addSubview(thicknessSlider)
        preheatContainer.addSubview(thicknessValueLabel)
        preheatContainer.addSubview(heatSourceLabel)
        preheatContainer.addSubview(heatSourceSegment)
        preheatContainer.addSubview(temperatureLabel)
        preheatContainer.addSubview(temperatureSlider)
        preheatContainer.addSubview(temperatureValueLabel)
        preheatContainer.addSubview(calculateButton)
        preheatContainer.addSubview(resultCard)

        resultCard.addSubview(timeResultLabel)
        resultCard.addSubview(modeResultLabel)
        resultCard.addSubview(riskResultLabel)
        resultCard.addSubview(tipsStackView)
    }

    private func setupStirCalculator() {
        stirTitleLabel.text = "Stir Rhythm Calculator"
        stirTitleLabel.font = AppTheme.Typography.title1
        stirTitleLabel.textColor = AppTheme.Colors.primaryText
        stirTitleLabel.textAlignment = .center
        stirTitleLabel.numberOfLines = 0

        stirComingSoonLabel.text = "🥄 Coming Soon!\n\nOptimal stirring patterns\nfor different cooking techniques\n\nStay tuned!"
        stirComingSoonLabel.font = AppTheme.Typography.title2
        stirComingSoonLabel.textColor = AppTheme.Colors.secondaryText
        stirComingSoonLabel.textAlignment = .center
        stirComingSoonLabel.numberOfLines = 0

        stirContainer.addSubview(stirTitleLabel)
        stirContainer.addSubview(stirComingSoonLabel)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(44)
        }

        preheatContainer.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        stirContainer.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        setupPreheatConstraints()
        setupStirConstraints()
    }

    private func setupPreheatConstraints() {
        materialLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        materialSegment.snp.makeConstraints { make in
            make.top.equalTo(materialLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        thicknessLabel.snp.makeConstraints { make in
            make.top.equalTo(materialSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        thicknessValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(thicknessLabel)
            make.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        thicknessSlider.snp.makeConstraints { make in
            make.top.equalTo(thicknessLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatSourceLabel.snp.makeConstraints { make in
            make.top.equalTo(thicknessSlider.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatSourceSegment.snp.makeConstraints { make in
            make.top.equalTo(heatSourceLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        temperatureLabel.snp.makeConstraints { make in
            make.top.equalTo(heatSourceSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        temperatureValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(temperatureLabel)
            make.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        temperatureSlider.snp.makeConstraints { make in
            make.top.equalTo(temperatureLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        calculateButton.snp.makeConstraints { make in
            make.top.equalTo(temperatureSlider.snp.bottom).offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(50)
        }

        resultCard.snp.makeConstraints { make in
            make.top.equalTo(calculateButton.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        timeResultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        modeResultLabel.snp.makeConstraints { make in
            make.top.equalTo(timeResultLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        riskResultLabel.snp.makeConstraints { make in
            make.top.equalTo(modeResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        tipsStackView.snp.makeConstraints { make in
            make.top.equalTo(riskResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupStirConstraints() {
        stirTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        stirComingSoonLabel.snp.makeConstraints { make in
            make.top.equalTo(stirTitleLabel.snp.bottom).offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }
    }

    private func setupActions() {
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        thicknessSlider.addTarget(self, action: #selector(thicknessChanged), for: .valueChanged)
        temperatureSlider.addTarget(self, action: #selector(temperatureChanged), for: .valueChanged)
        calculateButton.addTarget(self, action: #selector(calculateTapped), for: .touchUpInside)
    }

    // MARK: - Actions

    @objc private func segmentChanged() {
        switch segmentedControl.selectedSegmentIndex {
        case 0:
            showPreheatCalculator()
        case 1:
            showStirCalculator()
        default:
            break
        }
    }

    @objc private func thicknessChanged() {
        thicknessValueLabel.text = String(format: "%.1f mm", thicknessSlider.value)
    }

    @objc private func temperatureChanged() {
        temperatureValueLabel.text = String(format: "%.0f°C", temperatureSlider.value)
    }

    @objc private func calculateTapped() {
        performCalculation()
    }

    private func showPreheatCalculator() {
        UIView.animate(withDuration: 0.3) {
            self.preheatContainer.isHidden = false
            self.stirContainer.isHidden = true
            self.preheatContainer.alpha = 1.0
            self.stirContainer.alpha = 0.0
        }
    }

    private func showStirCalculator() {
        UIView.animate(withDuration: 0.3) {
            self.preheatContainer.isHidden = true
            self.stirContainer.isHidden = false
            self.preheatContainer.alpha = 0.0
            self.stirContainer.alpha = 1.0
        }
    }

    // MARK: - Calculation Logic

    private func performCalculation() {
        let materialIndex = materialSegment.selectedSegmentIndex
        let thickness = Double(thicknessSlider.value)
        let heatSourceIndex = heatSourceSegment.selectedSegmentIndex
        let ambientTemp = Double(temperatureSlider.value)

        let result = calculatePreheatTime(
            materialIndex: materialIndex,
            thickness: thickness,
            heatSourceIndex: heatSourceIndex,
            ambientTemperature: ambientTemp
        )

        displayResult(result)
    }

    private func calculatePreheatTime(materialIndex: Int, thickness: Double, heatSourceIndex: Int, ambientTemperature: Double) -> PreheatResult {
        // Material thermal properties (thermal conductivity W/m·K)
        let materialProperties: [Double] = [16.0, 52.0, 45.0, 205.0] // SS, Cast Iron, Carbon Steel, Aluminum
        let materialNames = ["Stainless Steel", "Cast Iron", "Carbon Steel", "Aluminum"]

        // Heat source power factors
        let heatSourcePowers: [Double] = [1.0, 0.8, 1.2] // Gas, Electric, Induction
        let heatSourceNames = ["Gas", "Electric", "Induction"]

        let thermalConductivity = materialProperties[materialIndex]
        let heatPower = heatSourcePowers[heatSourceIndex]

        // Base calculation: time = thickness^2 / (thermal_conductivity * heat_power * temp_factor)
        let tempFactor = max(0.5, (25.0 - ambientTemperature) / 10.0 + 1.0)
        let baseTime = (thickness * thickness) / (thermalConductivity * heatPower * tempFactor) * 60.0

        // Recommended time (add safety margin)
        let recommendedTime = max(30.0, baseTime * 1.2)

        // Determine heat mode
        let heatMode: HeatMode
        if thickness <= 2.0 {
            heatMode = .low
        } else if thickness <= 4.0 {
            heatMode = .medium
        } else {
            heatMode = .high
        }

        // Determine overheating risk
        let overheatingRisk: OverheatingRisk
        if materialIndex == 3 { // Aluminum
            overheatingRisk = .high
        } else if thickness <= 2.0 {
            overheatingRisk = .medium
        } else {
            overheatingRisk = .low
        }

        // Generate tips
        var tips: [String] = []
        tips.append("Start with \(heatMode.displayName.lowercased()) heat")

        if overheatingRisk == .high {
            tips.append("Watch carefully - \(materialNames[materialIndex].lowercased()) heats quickly")
        }

        if heatSourceIndex == 2 { // Induction
            tips.append("Induction provides even heating - perfect for this pan")
        }

        if ambientTemperature < 20 {
            tips.append("Cold kitchen - allow extra time for preheating")
        }

        tips.append("Test with water droplets - they should sizzle and evaporate")

        return PreheatResult(
            recommendedTime: recommendedTime,
            heatMode: heatMode,
            overheatingRisk: overheatingRisk,
            tips: tips
        )
    }

    private func displayResult(_ result: PreheatResult) {
        let minutes = Int(result.recommendedTime) / 60
        let seconds = Int(result.recommendedTime) % 60

        if minutes > 0 {
            timeResultLabel.text = String(format: "⏱️ %d:%02d minutes", minutes, seconds)
        } else {
            timeResultLabel.text = String(format: "⏱️ %d seconds", seconds)
        }

        modeResultLabel.text = "🔥 \(result.heatMode.displayName)\n\(result.heatMode.description)"

        switch result.overheatingRisk {
        case .low:
            riskResultLabel.textColor = AppTheme.Colors.success
            riskResultLabel.text = "✅ \(result.overheatingRisk.displayName)"
        case .medium:
            riskResultLabel.textColor = AppTheme.Colors.warning
            riskResultLabel.text = "⚠️ \(result.overheatingRisk.displayName)"
        case .high:
            riskResultLabel.textColor = AppTheme.Colors.error
            riskResultLabel.text = "🚨 \(result.overheatingRisk.displayName)"
        }

        // Clear previous tips
        tipsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add new tips
        for tip in result.tips {
            let tipLabel = UILabel()
            tipLabel.text = "• \(tip)"
            tipLabel.font = AppTheme.Typography.caption1
            tipLabel.textColor = AppTheme.Colors.secondaryText
            tipLabel.numberOfLines = 0
            tipsStackView.addArrangedSubview(tipLabel)
        }

        // Show result card with animation
        if resultCard.isHidden {
            resultCard.isHidden = false
            resultCard.alpha = 0
            UIView.animate(withDuration: 0.3) {
                self.resultCard.alpha = 1
            }
        }
    }
}