//
//  SmartHeatCalcViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit
import AAInfographics

// MARK: - Data Models

struct PreheatResult {
    let recommendedTime: Double // in seconds
    let heatMode: HeatMode
    let overheatingRisk: OverheatingRisk
    let tips: [String]
}

enum HeatMode {
    case low, medium, high

    var displayName: String {
        switch self {
        case .low: return "Low Heat"
        case .medium: return "Medium Heat"
        case .high: return "High Heat"
        }
    }

    var description: String {
        switch self {
        case .low: return "Gentle warming, perfect for delicate foods"
        case .medium: return "Standard preheating for most cooking"
        case .high: return "Quick heating for searing and high-temp cooking"
        }
    }
}

enum OverheatingRisk {
    case low, medium, high

    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        }
    }
}

struct StirResult {
    let rhythm: StirRhythm
    let frequency: StirFrequency
    let technique: StirTechnique
    let tips: [String]
}

enum StirRhythm {
    case gentle, steady, vigorous, rapid

    var displayName: String {
        switch self {
        case .gentle: return "Gentle Rhythm"
        case .steady: return "Steady Rhythm"
        case .vigorous: return "Vigorous Rhythm"
        case .rapid: return "Rapid Rhythm"
        }
    }

    var description: String {
        switch self {
        case .gentle: return "Slow, careful movements"
        case .steady: return "Consistent, controlled stirring"
        case .vigorous: return "Active, energetic mixing"
        case .rapid: return "Quick, continuous motion"
        }
    }
}

enum StirFrequency {
    case occasional, regular, frequent, constant

    var displayName: String {
        switch self {
        case .occasional: return "Every 30-60 seconds"
        case .regular: return "Every 15-30 seconds"
        case .frequent: return "Every 5-15 seconds"
        case .constant: return "Continuous stirring"
        }
    }
}

enum StirTechnique {
    case folding, tossing, circular, figure8

    var displayName: String {
        switch self {
        case .folding: return "🥄 Folding Technique"
        case .tossing: return "🍳 Tossing Motion"
        case .circular: return "🔄 Circular Stirring"
        case .figure8: return "∞ Figure-8 Pattern"
        }
    }

    var description: String {
        switch self {
        case .folding: return "Gentle lifting and folding motion"
        case .tossing: return "Quick wrist flicks to toss ingredients"
        case .circular: return "Smooth circular movements"
        case .figure8: return "Figure-8 pattern for even mixing"
        }
    }
}



class SmartHeatCalcViewController: UIViewController {

    // MARK: - UI Components

    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let titleLabel = UILabel()
    private let segmentedControl = UISegmentedControl(items: ["Preheat Calculator", "Stir Rhythm"])

    // Container views for each calculator
    private let preheatContainer = UIView()
    private let stirContainer = UIView()
    private let thermoTrendContainer = UIView()

    // Preheat Calculator Components
    private let materialLabel = UILabel()
    private let materialSegment = UISegmentedControl(items: ["Stainless Steel", "Cast Iron", "Carbon Steel", "Aluminum"])

    private let thicknessLabel = UILabel()
    private let thicknessSlider = UISlider()
    private let thicknessValueLabel = UILabel()

    private let heatSourceLabel = UILabel()
    private let heatSourceSegment = UISegmentedControl(items: ["Gas", "Electric", "Induction"])

    private let temperatureLabel = UILabel()
    private let temperatureSlider = UISlider()
    private let temperatureValueLabel = UILabel()

    private let calculateButton = UIButton(type: .system)

    // Result display
    private let resultCard = UIView()
    private let timeResultLabel = UILabel()
    private let modeResultLabel = UILabel()
    private let riskResultLabel = UILabel()
    private let tipsStackView = UIStackView()

    // Stir Rhythm Components
    private let cookingMethodLabel = UILabel()
    private let cookingMethodSegment = UISegmentedControl(items: ["Sautéing", "Stir-fry", "Sauce", "Risotto"])

    private let ingredientLabel = UILabel()
    private let ingredientSegment = UISegmentedControl(items: ["Vegetables", "Meat", "Seafood", "Grains"])

    private let panSizeLabel = UILabel()
    private let panSizeSlider = UISlider()
    private let panSizeValueLabel = UILabel()

    private let heatLevelLabel = UILabel()
    private let heatLevelSegment = UISegmentedControl(items: ["Low", "Medium", "High"])

    private let calculateStirButton = UIButton(type: .system)

    // Stir Result display
    private let stirResultCard = UIView()
    private let rhythmResultLabel = UILabel()
    private let frequencyResultLabel = UILabel()
    private let techniqueResultLabel = UILabel()
    private let stirTipsStackView = UIStackView()

    // ThermoTrend Components
    private let panSelectionLabel = UILabel()
    private let panSelectionSegment = UISegmentedControl(items: ["Pan A", "Pan B", "Pan C", "Compare All"])

    private let chartTypeLabel = UILabel()
    private let chartTypeSegment = UISegmentedControl(items: ["Performance", "Temperature", "Burn Risk"])

    private let timeRangeLabel = UILabel()
    private let timeRangeSegment = UISegmentedControl(items: ["7 Days", "30 Days", "90 Days"])

    private let chartScrollView = UIScrollView()
    private let chartContainerView = UIView()

    private let analysisCard = UIView()
    private let trendSummaryLabel = UILabel()
    private let recommendationLabel = UILabel()
    private let insightsStackView = UIStackView()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()

        // Show preheat calculator by default
        showPreheatCalculator()
    }

    // MARK: - Setup Methods

    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "SmartHeat Calc"

        // Configure scroll view
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        // Configure title with gradient effect
        titleLabel.text = "🔥 SmartHeat Calculator"
        titleLabel.font = AppTheme.Typography.largeTitle.withSize(28)
        titleLabel.textColor = AppTheme.Colors.primary
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0

        // Add subtle shadow to title
        titleLabel.layer.shadowColor = UIColor.black.cgColor
        titleLabel.layer.shadowOffset = CGSize(width: 0, height: 1)
        titleLabel.layer.shadowOpacity = 0.1
        titleLabel.layer.shadowRadius = 2

        // Configure segmented control with modern styling
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.backgroundColor = AppTheme.Colors.surface
        segmentedControl.selectedSegmentTintColor = AppTheme.Colors.primary
        segmentedControl.layer.cornerRadius = 12
        segmentedControl.layer.shadowColor = UIColor.black.cgColor
        segmentedControl.layer.shadowOffset = CGSize(width: 0, height: 2)
        segmentedControl.layer.shadowOpacity = 0.1
        segmentedControl.layer.shadowRadius = 4

        segmentedControl.setTitleTextAttributes([
            .foregroundColor: AppTheme.Colors.secondaryText,
            .font: AppTheme.Typography.headline
        ], for: .normal)
        segmentedControl.setTitleTextAttributes([
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
        ], for: .selected)

        // Configure containers
        preheatContainer.backgroundColor = .clear
        stirContainer.backgroundColor = .clear
        thermoTrendContainer.backgroundColor = .clear
        stirContainer.isHidden = true
        thermoTrendContainer.isHidden = true

        setupPreheatCalculator()
        setupStirCalculator()

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(titleLabel)
        contentView.addSubview(segmentedControl)
        contentView.addSubview(preheatContainer)
        contentView.addSubview(stirContainer)
        contentView.addSubview(thermoTrendContainer)
    }

    private func setupPreheatCalculator() {
        // Material selection with enhanced styling
        materialLabel.text = "🍳 Pan Material"
        materialLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        materialLabel.textColor = AppTheme.Colors.primaryText

        materialSegment.selectedSegmentIndex = 0
        materialSegment.backgroundColor = AppTheme.Colors.surface
        materialSegment.selectedSegmentTintColor = AppTheme.Colors.primary
        materialSegment.layer.cornerRadius = 8
        materialSegment.layer.borderWidth = 1
        materialSegment.layer.borderColor = AppTheme.Colors.border.cgColor

        // Thickness slider with modern styling
        thicknessLabel.text = "📏 Pan Thickness"
        thicknessLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        thicknessLabel.textColor = AppTheme.Colors.primaryText

        thicknessSlider.minimumValue = 1.0
        thicknessSlider.maximumValue = 8.0
        thicknessSlider.value = 3.0
        thicknessSlider.tintColor = AppTheme.Colors.primary
        thicknessSlider.layer.cornerRadius = 2

        thicknessValueLabel.text = "3.0 mm"
        thicknessValueLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        thicknessValueLabel.textColor = AppTheme.Colors.primary
        thicknessValueLabel.backgroundColor = AppTheme.Colors.surface
        thicknessValueLabel.layer.cornerRadius = 8
        thicknessValueLabel.layer.masksToBounds = true
        thicknessValueLabel.textAlignment = .center

        // Heat source with icons
        heatSourceLabel.text = "🔥 Heat Source"
        heatSourceLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        heatSourceLabel.textColor = AppTheme.Colors.primaryText

        heatSourceSegment.selectedSegmentIndex = 0
        heatSourceSegment.backgroundColor = AppTheme.Colors.surface
        heatSourceSegment.selectedSegmentTintColor = AppTheme.Colors.primary
        heatSourceSegment.layer.cornerRadius = 8
        heatSourceSegment.layer.borderWidth = 1
        heatSourceSegment.layer.borderColor = AppTheme.Colors.border.cgColor

        // Temperature slider with enhanced styling
        temperatureLabel.text = "🌡️ Ambient Temperature"
        temperatureLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        temperatureLabel.textColor = AppTheme.Colors.primaryText

        temperatureSlider.minimumValue = 15.0
        temperatureSlider.maximumValue = 35.0
        temperatureSlider.value = 22.0
        temperatureSlider.tintColor = AppTheme.Colors.primary
        temperatureSlider.layer.cornerRadius = 2

        temperatureValueLabel.text = "22°C"
        temperatureValueLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        temperatureValueLabel.textColor = AppTheme.Colors.primary
        temperatureValueLabel.backgroundColor = AppTheme.Colors.surface
        temperatureValueLabel.layer.cornerRadius = 8
        temperatureValueLabel.layer.masksToBounds = true
        temperatureValueLabel.textAlignment = .center

        // Enhanced calculate button
        calculateButton.setTitle("⚡ Calculate Preheat Time", for: .normal)
        calculateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        calculateButton.backgroundColor = AppTheme.Colors.primary
        calculateButton.setTitleColor(.white, for: .normal)
        calculateButton.layer.cornerRadius = 16
        calculateButton.layer.shadowColor = AppTheme.Colors.primary.cgColor
        calculateButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        calculateButton.layer.shadowOpacity = 0.3
        calculateButton.layer.shadowRadius = 8

        // Enhanced result card
        resultCard.backgroundColor = AppTheme.Colors.surface
        resultCard.layer.cornerRadius = 20
        resultCard.layer.shadowColor = UIColor.black.cgColor
        resultCard.layer.shadowOffset = CGSize(width: 0, height: 4)
        resultCard.layer.shadowRadius = 12
        resultCard.layer.shadowOpacity = 0.15
        resultCard.layer.borderWidth = 1
        resultCard.layer.borderColor = AppTheme.Colors.border.cgColor
        resultCard.isHidden = true

        timeResultLabel.font = AppTheme.Typography.title2
        timeResultLabel.textColor = AppTheme.Colors.primaryText
        timeResultLabel.textAlignment = .center
        timeResultLabel.numberOfLines = 0

        modeResultLabel.font = AppTheme.Typography.body
        modeResultLabel.textColor = AppTheme.Colors.secondaryText
        modeResultLabel.textAlignment = .center
        modeResultLabel.numberOfLines = 0

        riskResultLabel.font = AppTheme.Typography.headline
        riskResultLabel.textAlignment = .center
        riskResultLabel.numberOfLines = 0

        tipsStackView.axis = .vertical
        tipsStackView.spacing = AppTheme.Spacing.sm
        tipsStackView.alignment = .leading

        // Add preheat components to container
        preheatContainer.addSubview(materialLabel)
        preheatContainer.addSubview(materialSegment)
        preheatContainer.addSubview(thicknessLabel)
        preheatContainer.addSubview(thicknessSlider)
        preheatContainer.addSubview(thicknessValueLabel)
        preheatContainer.addSubview(heatSourceLabel)
        preheatContainer.addSubview(heatSourceSegment)
        preheatContainer.addSubview(temperatureLabel)
        preheatContainer.addSubview(temperatureSlider)
        preheatContainer.addSubview(temperatureValueLabel)
        preheatContainer.addSubview(calculateButton)
        preheatContainer.addSubview(resultCard)

        resultCard.addSubview(timeResultLabel)
        resultCard.addSubview(modeResultLabel)
        resultCard.addSubview(riskResultLabel)
        resultCard.addSubview(tipsStackView)
    }

    private func setupStirCalculator() {
        // Cooking method selection with enhanced styling
        cookingMethodLabel.text = "👨‍🍳 Cooking Method"
        cookingMethodLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        cookingMethodLabel.textColor = AppTheme.Colors.primaryText

        cookingMethodSegment.selectedSegmentIndex = 0
        cookingMethodSegment.backgroundColor = AppTheme.Colors.surface
        cookingMethodSegment.selectedSegmentTintColor = AppTheme.Colors.primary
        cookingMethodSegment.layer.cornerRadius = 8
        cookingMethodSegment.layer.borderWidth = 1
        cookingMethodSegment.layer.borderColor = AppTheme.Colors.border.cgColor

        // Ingredient type with icons
        ingredientLabel.text = "🥘 Main Ingredient"
        ingredientLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        ingredientLabel.textColor = AppTheme.Colors.primaryText

        ingredientSegment.selectedSegmentIndex = 0
        ingredientSegment.backgroundColor = AppTheme.Colors.surface
        ingredientSegment.selectedSegmentTintColor = AppTheme.Colors.primary
        ingredientSegment.layer.cornerRadius = 8
        ingredientSegment.layer.borderWidth = 1
        ingredientSegment.layer.borderColor = AppTheme.Colors.border.cgColor

        // Pan size slider with modern styling
        panSizeLabel.text = "🍳 Pan Size"
        panSizeLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        panSizeLabel.textColor = AppTheme.Colors.primaryText

        panSizeSlider.minimumValue = 20.0
        panSizeSlider.maximumValue = 35.0
        panSizeSlider.value = 26.0
        panSizeSlider.tintColor = AppTheme.Colors.primary
        panSizeSlider.layer.cornerRadius = 2

        panSizeValueLabel.text = "26 cm"
        panSizeValueLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        panSizeValueLabel.textColor = AppTheme.Colors.primary
        panSizeValueLabel.backgroundColor = AppTheme.Colors.surface
        panSizeValueLabel.layer.cornerRadius = 8
        panSizeValueLabel.layer.masksToBounds = true
        panSizeValueLabel.textAlignment = .center

        // Heat level with enhanced styling
        heatLevelLabel.text = "🔥 Heat Level"
        heatLevelLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        heatLevelLabel.textColor = AppTheme.Colors.primaryText

        heatLevelSegment.selectedSegmentIndex = 1
        heatLevelSegment.backgroundColor = AppTheme.Colors.surface
        heatLevelSegment.selectedSegmentTintColor = AppTheme.Colors.primary
        heatLevelSegment.layer.cornerRadius = 8
        heatLevelSegment.layer.borderWidth = 1
        heatLevelSegment.layer.borderColor = AppTheme.Colors.border.cgColor

        // Enhanced calculate button
        calculateStirButton.setTitle("🥄 Calculate Stir Rhythm", for: .normal)
        calculateStirButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        calculateStirButton.backgroundColor = AppTheme.Colors.primary
        calculateStirButton.setTitleColor(.white, for: .normal)
        calculateStirButton.layer.cornerRadius = 16
        calculateStirButton.layer.shadowColor = AppTheme.Colors.primary.cgColor
        calculateStirButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        calculateStirButton.layer.shadowOpacity = 0.3
        calculateStirButton.layer.shadowRadius = 8

        // Enhanced result card
        stirResultCard.backgroundColor = AppTheme.Colors.surface
        stirResultCard.layer.cornerRadius = 20
        stirResultCard.layer.shadowColor = UIColor.black.cgColor
        stirResultCard.layer.shadowOffset = CGSize(width: 0, height: 4)
        stirResultCard.layer.shadowRadius = 12
        stirResultCard.layer.shadowOpacity = 0.15
        stirResultCard.layer.borderWidth = 1
        stirResultCard.layer.borderColor = AppTheme.Colors.border.cgColor
        stirResultCard.isHidden = true

        rhythmResultLabel.font = AppTheme.Typography.title2
        rhythmResultLabel.textColor = AppTheme.Colors.primaryText
        rhythmResultLabel.textAlignment = .center
        rhythmResultLabel.numberOfLines = 0

        frequencyResultLabel.font = AppTheme.Typography.body
        frequencyResultLabel.textColor = AppTheme.Colors.secondaryText
        frequencyResultLabel.textAlignment = .center
        frequencyResultLabel.numberOfLines = 0

        techniqueResultLabel.font = AppTheme.Typography.headline
        techniqueResultLabel.textAlignment = .center
        techniqueResultLabel.numberOfLines = 0

        stirTipsStackView.axis = .vertical
        stirTipsStackView.spacing = AppTheme.Spacing.sm
        stirTipsStackView.alignment = .leading

        // Add stir components to container
        stirContainer.addSubview(cookingMethodLabel)
        stirContainer.addSubview(cookingMethodSegment)
        stirContainer.addSubview(ingredientLabel)
        stirContainer.addSubview(ingredientSegment)
        stirContainer.addSubview(panSizeLabel)
        stirContainer.addSubview(panSizeSlider)
        stirContainer.addSubview(panSizeValueLabel)
        stirContainer.addSubview(heatLevelLabel)
        stirContainer.addSubview(heatLevelSegment)
        stirContainer.addSubview(calculateStirButton)
        stirContainer.addSubview(stirResultCard)

        stirResultCard.addSubview(rhythmResultLabel)
        stirResultCard.addSubview(frequencyResultLabel)
        stirResultCard.addSubview(techniqueResultLabel)
        stirResultCard.addSubview(stirTipsStackView)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(44)
        }

        preheatContainer.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        stirContainer.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        setupPreheatConstraints()
        setupStirConstraints()
    }

    private func setupPreheatConstraints() {
        materialLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        materialSegment.snp.makeConstraints { make in
            make.top.equalTo(materialLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        thicknessLabel.snp.makeConstraints { make in
            make.top.equalTo(materialSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        thicknessValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(thicknessLabel)
            make.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        thicknessSlider.snp.makeConstraints { make in
            make.top.equalTo(thicknessLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatSourceLabel.snp.makeConstraints { make in
            make.top.equalTo(thicknessSlider.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatSourceSegment.snp.makeConstraints { make in
            make.top.equalTo(heatSourceLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        temperatureLabel.snp.makeConstraints { make in
            make.top.equalTo(heatSourceSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        temperatureValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(temperatureLabel)
            make.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        temperatureSlider.snp.makeConstraints { make in
            make.top.equalTo(temperatureLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        calculateButton.snp.makeConstraints { make in
            make.top.equalTo(temperatureSlider.snp.bottom).offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(50)
        }

        resultCard.snp.makeConstraints { make in
            make.top.equalTo(calculateButton.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        timeResultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        modeResultLabel.snp.makeConstraints { make in
            make.top.equalTo(timeResultLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        riskResultLabel.snp.makeConstraints { make in
            make.top.equalTo(modeResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        tipsStackView.snp.makeConstraints { make in
            make.top.equalTo(riskResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupStirConstraints() {
        cookingMethodLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        cookingMethodSegment.snp.makeConstraints { make in
            make.top.equalTo(cookingMethodLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        ingredientLabel.snp.makeConstraints { make in
            make.top.equalTo(cookingMethodSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        ingredientSegment.snp.makeConstraints { make in
            make.top.equalTo(ingredientLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        panSizeLabel.snp.makeConstraints { make in
            make.top.equalTo(ingredientSegment.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        panSizeValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(panSizeLabel)
            make.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        panSizeSlider.snp.makeConstraints { make in
            make.top.equalTo(panSizeLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatLevelLabel.snp.makeConstraints { make in
            make.top.equalTo(panSizeSlider.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        heatLevelSegment.snp.makeConstraints { make in
            make.top.equalTo(heatLevelLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(32)
        }

        calculateStirButton.snp.makeConstraints { make in
            make.top.equalTo(heatLevelSegment.snp.bottom).offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(50)
        }

        stirResultCard.snp.makeConstraints { make in
            make.top.equalTo(calculateStirButton.snp.bottom).offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }

        rhythmResultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        frequencyResultLabel.snp.makeConstraints { make in
            make.top.equalTo(rhythmResultLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        techniqueResultLabel.snp.makeConstraints { make in
            make.top.equalTo(frequencyResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        stirTipsStackView.snp.makeConstraints { make in
            make.top.equalTo(techniqueResultLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupActions() {
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        thicknessSlider.addTarget(self, action: #selector(thicknessChanged), for: .valueChanged)
        temperatureSlider.addTarget(self, action: #selector(temperatureChanged), for: .valueChanged)
        calculateButton.addTarget(self, action: #selector(calculateTapped), for: .touchUpInside)
        calculateButton.addTarget(self, action: #selector(buttonPressed), for: .touchDown)
        calculateButton.addTarget(self, action: #selector(buttonReleased), for: [.touchUpInside, .touchUpOutside, .touchCancel])

        panSizeSlider.addTarget(self, action: #selector(panSizeChanged), for: .valueChanged)
        calculateStirButton.addTarget(self, action: #selector(calculateStirTapped), for: .touchUpInside)
        calculateStirButton.addTarget(self, action: #selector(buttonPressed), for: .touchDown)
        calculateStirButton.addTarget(self, action: #selector(buttonReleased), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }

    // MARK: - Actions

    @objc private func segmentChanged() {
        // Add smooth transition animation
        UIView.transition(with: view, duration: 0.3, options: .transitionCrossDissolve) {
            switch self.segmentedControl.selectedSegmentIndex {
            case 0:
                self.showPreheatCalculator()
            case 1:
                self.showStirCalculator()
            default:
                break
            }
        }
    }

    @objc private func thicknessChanged() {
        thicknessValueLabel.text = String(format: "%.1f mm", thicknessSlider.value)
    }

    @objc private func temperatureChanged() {
        temperatureValueLabel.text = String(format: "%.0f°C", temperatureSlider.value)
    }

    @objc private func calculateTapped() {
        performCalculation()
    }

    @objc private func panSizeChanged() {
        panSizeValueLabel.text = String(format: "%.0f cm", panSizeSlider.value)
    }

    // MARK: - Button Animation Methods

    @objc private func buttonPressed(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1, delay: 0, options: [.allowUserInteraction, .curveEaseInOut]) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            sender.alpha = 0.8
        }
    }

    @objc private func buttonReleased(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1, delay: 0, options: [.allowUserInteraction, .curveEaseInOut]) {
            sender.transform = CGAffineTransform.identity
            sender.alpha = 1.0
        }
    }

    @objc private func calculateStirTapped() {
        performStirCalculation()
    }

    private func showPreheatCalculator() {
        UIView.animate(withDuration: 0.3) {
            self.preheatContainer.isHidden = false
            self.stirContainer.isHidden = true
            self.preheatContainer.alpha = 1.0
            self.stirContainer.alpha = 0.0
        }
    }

    private func showStirCalculator() {
        UIView.animate(withDuration: 0.3) {
            self.preheatContainer.isHidden = true
            self.stirContainer.isHidden = false
            self.preheatContainer.alpha = 0.0
            self.stirContainer.alpha = 1.0
        }
    }

    // MARK: - Calculation Logic

    private func performCalculation() {
        let materialIndex = materialSegment.selectedSegmentIndex
        let thickness = Double(thicknessSlider.value)
        let heatSourceIndex = heatSourceSegment.selectedSegmentIndex
        let ambientTemp = Double(temperatureSlider.value)

        let result = calculatePreheatTime(
            materialIndex: materialIndex,
            thickness: thickness,
            heatSourceIndex: heatSourceIndex,
            ambientTemperature: ambientTemp
        )

        displayResult(result)
    }

    private func calculatePreheatTime(materialIndex: Int, thickness: Double, heatSourceIndex: Int, ambientTemperature: Double) -> PreheatResult {
        // Material thermal properties (thermal conductivity W/m·K)
        let materialProperties: [Double] = [16.0, 52.0, 45.0, 205.0] // SS, Cast Iron, Carbon Steel, Aluminum
        let materialNames = ["Stainless Steel", "Cast Iron", "Carbon Steel", "Aluminum"]

        // Heat source power factors
        let heatSourcePowers: [Double] = [1.0, 0.8, 1.2] // Gas, Electric, Induction
        let heatSourceNames = ["Gas", "Electric", "Induction"]

        let thermalConductivity = materialProperties[materialIndex]
        let heatPower = heatSourcePowers[heatSourceIndex]

        // Base calculation: time = thickness^2 / (thermal_conductivity * heat_power * temp_factor)
        let tempFactor = max(0.5, (25.0 - ambientTemperature) / 10.0 + 1.0)
        let baseTime = (thickness * thickness) / (thermalConductivity * heatPower * tempFactor) * 60.0

        // Recommended time (add safety margin)
        let recommendedTime = max(30.0, baseTime * 1.2)

        // Determine heat mode
        let heatMode: HeatMode
        if thickness <= 2.0 {
            heatMode = .low
        } else if thickness <= 4.0 {
            heatMode = .medium
        } else {
            heatMode = .high
        }

        // Determine overheating risk
        let overheatingRisk: OverheatingRisk
        if materialIndex == 3 { // Aluminum
            overheatingRisk = .high
        } else if thickness <= 2.0 {
            overheatingRisk = .medium
        } else {
            overheatingRisk = .low
        }

        // Generate tips
        var tips: [String] = []
        tips.append("Start with \(heatMode.displayName.lowercased()) heat")

        if overheatingRisk == .high {
            tips.append("Watch carefully - \(materialNames[materialIndex].lowercased()) heats quickly")
        }

        if heatSourceIndex == 2 { // Induction
            tips.append("Induction provides even heating - perfect for this pan")
        }

        if ambientTemperature < 20 {
            tips.append("Cold kitchen - allow extra time for preheating")
        }

        tips.append("Test with water droplets - they should sizzle and evaporate")

        return PreheatResult(
            recommendedTime: recommendedTime,
            heatMode: heatMode,
            overheatingRisk: overheatingRisk,
            tips: tips
        )
    }

    private func displayResult(_ result: PreheatResult) {
        let minutes = Int(result.recommendedTime) / 60
        let seconds = Int(result.recommendedTime) % 60

        if minutes > 0 {
            timeResultLabel.text = String(format: "⏱️ %d:%02d minutes", minutes, seconds)
        } else {
            timeResultLabel.text = String(format: "⏱️ %d seconds", seconds)
        }

        modeResultLabel.text = "🔥 \(result.heatMode.displayName)\n\(result.heatMode.description)"

        switch result.overheatingRisk {
        case .low:
            riskResultLabel.textColor = AppTheme.Colors.success
            riskResultLabel.text = "✅ \(result.overheatingRisk.displayName)"
        case .medium:
            riskResultLabel.textColor = AppTheme.Colors.warning
            riskResultLabel.text = "⚠️ \(result.overheatingRisk.displayName)"
        case .high:
            riskResultLabel.textColor = AppTheme.Colors.error
            riskResultLabel.text = "🚨 \(result.overheatingRisk.displayName)"
        }

        // Clear previous tips
        tipsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add new tips
        for tip in result.tips {
            let tipLabel = UILabel()
            tipLabel.text = "• \(tip)"
            tipLabel.font = AppTheme.Typography.caption1
            tipLabel.textColor = AppTheme.Colors.secondaryText
            tipLabel.numberOfLines = 0
            tipsStackView.addArrangedSubview(tipLabel)
        }

        // Show result card with enhanced animation
        if resultCard.isHidden {
            resultCard.isHidden = false
            resultCard.alpha = 0
            resultCard.transform = CGAffineTransform(translationX: 0, y: 20)

            UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: .curveEaseOut) {
                self.resultCard.alpha = 1
                self.resultCard.transform = CGAffineTransform.identity
            }
        }
    }

    // MARK: - Stir Calculation Logic

    private func performStirCalculation() {
        let cookingMethodIndex = cookingMethodSegment.selectedSegmentIndex
        let ingredientIndex = ingredientSegment.selectedSegmentIndex
        let panSize = Double(panSizeSlider.value)
        let heatLevelIndex = heatLevelSegment.selectedSegmentIndex

        let result = calculateStirRhythm(
            cookingMethodIndex: cookingMethodIndex,
            ingredientIndex: ingredientIndex,
            panSize: panSize,
            heatLevelIndex: heatLevelIndex
        )

        displayStirResult(result)
    }

    private func calculateStirRhythm(cookingMethodIndex: Int, ingredientIndex: Int, panSize: Double, heatLevelIndex: Int) -> StirResult {
        let cookingMethods = ["Sautéing", "Stir-fry", "Sauce", "Risotto"]
        let ingredients = ["Vegetables", "Meat", "Seafood", "Grains"]
        let heatLevels = ["Low", "Medium", "High"]

        // Determine rhythm based on cooking method and heat level
        let rhythm: StirRhythm
        switch cookingMethodIndex {
        case 0: // Sautéing
            rhythm = heatLevelIndex >= 2 ? .vigorous : .steady
        case 1: // Stir-fry
            rhythm = .rapid
        case 2: // Sauce
            rhythm = heatLevelIndex <= 1 ? .gentle : .steady
        case 3: // Risotto
            rhythm = .steady
        default:
            rhythm = .steady
        }

        // Determine frequency based on cooking method and ingredient
        let frequency: StirFrequency
        switch cookingMethodIndex {
        case 0: // Sautéing
            frequency = ingredientIndex == 1 ? .regular : .frequent // Meat needs less frequent stirring
        case 1: // Stir-fry
            frequency = .constant
        case 2: // Sauce
            frequency = .occasional
        case 3: // Risotto
            frequency = .regular
        default:
            frequency = .regular
        }

        // Determine technique based on cooking method and pan size
        let technique: StirTechnique
        switch cookingMethodIndex {
        case 0: // Sautéing
            technique = panSize >= 28 ? .tossing : .circular
        case 1: // Stir-fry
            technique = .tossing
        case 2: // Sauce
            technique = .figure8
        case 3: // Risotto
            technique = .folding
        default:
            technique = .circular
        }

        // Generate tips
        var tips: [String] = []

        // Method-specific tips
        switch cookingMethodIndex {
        case 0: // Sautéing
            tips.append("Keep ingredients moving to prevent sticking")
            if panSize >= 28 {
                tips.append("Use wrist motion for tossing in larger pans")
            }
        case 1: // Stir-fry
            tips.append("Constant motion is key for even cooking")
            tips.append("Use high heat and quick movements")
        case 2: // Sauce
            tips.append("Stir gently to prevent breaking the sauce")
            tips.append("Scrape bottom to prevent burning")
        case 3: // Risotto
            tips.append("Gentle folding preserves rice texture")
            tips.append("Add liquid gradually while stirring")
        default:
            break
        }

        // Ingredient-specific tips
        switch ingredientIndex {
        case 0: // Vegetables
            tips.append("Different vegetables cook at different rates")
        case 1: // Meat
            tips.append("Let meat sear before stirring too much")
        case 2: // Seafood
            tips.append("Gentle handling to prevent breaking")
        case 3: // Grains
            tips.append("Ensure even heat distribution")
        default:
            break
        }

        // Heat level tips
        if heatLevelIndex >= 2 {
            tips.append("High heat requires more frequent stirring")
        }

        // Pan size tips
        if panSize <= 22 {
            tips.append("Small pan - avoid overcrowding")
        } else if panSize >= 30 {
            tips.append("Large pan - ensure even coverage")
        }

        return StirResult(
            rhythm: rhythm,
            frequency: frequency,
            technique: technique,
            tips: tips
        )
    }

    private func displayStirResult(_ result: StirResult) {
        rhythmResultLabel.text = "🎵 \(result.rhythm.displayName)\n\(result.rhythm.description)"

        frequencyResultLabel.text = "⏰ Stir Frequency: \(result.frequency.displayName)"

        techniqueResultLabel.textColor = AppTheme.Colors.primary
        techniqueResultLabel.text = "\(result.technique.displayName)\n\(result.technique.description)"

        // Clear previous tips
        stirTipsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add new tips
        for tip in result.tips {
            let tipLabel = UILabel()
            tipLabel.text = "• \(tip)"
            tipLabel.font = AppTheme.Typography.caption1
            tipLabel.textColor = AppTheme.Colors.secondaryText
            tipLabel.numberOfLines = 0
            stirTipsStackView.addArrangedSubview(tipLabel)
        }

        // Show result card with enhanced animation
        if stirResultCard.isHidden {
            stirResultCard.isHidden = false
            stirResultCard.alpha = 0
            stirResultCard.transform = CGAffineTransform(translationX: 0, y: 20)

            UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: .curveEaseOut) {
                self.stirResultCard.alpha = 1
                self.stirResultCard.transform = CGAffineTransform.identity
            }
        }
    }
}
