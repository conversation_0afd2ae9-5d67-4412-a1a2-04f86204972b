//
//  SmartHeatCalcViewController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class SmartHeatCalcViewController: UIViewController {

    // MARK: - UI Components

    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let titleLabel = UILabel()
    private let comingSoonLabel = UILabel()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }

    // MARK: - Setup Methods

    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.background
        title = "SmartHeat Calc"

        // Configure scroll view
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        // Configure title
        titleLabel.text = "Multi-Function Heat Calculator"
        titleLabel.font = AppTheme.Typography.largeTitle
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0

        // Configure coming soon label
        comingSoonLabel.text = "🧮 Coming Soon!\n\nPreheat Time Estimator\n& Stir Rhythm Matcher\n\nStay tuned for advanced heat calculations!"
        comingSoonLabel.font = AppTheme.Typography.title2
        comingSoonLabel.textColor = AppTheme.Colors.secondaryText
        comingSoonLabel.textAlignment = .center
        comingSoonLabel.numberOfLines = 0

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(comingSoonLabel)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        comingSoonLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.xl)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }
    }
}