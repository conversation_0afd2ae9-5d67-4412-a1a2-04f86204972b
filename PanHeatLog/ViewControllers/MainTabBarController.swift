//
//  MainTabBarController.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
        setupViewControllers()
    }
    
    private func setupTabBar() {
        tabBar.backgroundColor = AppTheme.Colors.surface
        tabBar.tintColor = AppTheme.Colors.primary
        tabBar.unselectedItemTintColor = AppTheme.Colors.tertiaryText
        tabBar.barTintColor = AppTheme.Colors.surface
        
        // Add subtle border
        tabBar.layer.borderWidth = 0.5
        tabBar.layer.borderColor = AppTheme.Colors.border.cgColor
    }
    
    private func setupViewControllers() {
        // HeatScan Log Module
        let heatScanListVC = HeatScanListViewController()
        let heatScanNavController = UINavigationController(rootViewController: heatScanListVC)
        AppTheme.configureNavigationController(heatScanNavController)
        heatScanNavController.tabBarItem = UITabBarItem(
            title: "HeatScan Log",
            image: UIImage(systemName: "flame"),
            selectedImage: UIImage(systemName: "flame.fill")
        )
        
        // SmartHeat Calc Module
        let smartCalcVC = SmartHeatCalcViewController()
        let smartCalcNavController = UINavigationController(rootViewController: smartCalcVC)
        AppTheme.configureNavigationController(smartCalcNavController)
        smartCalcNavController.tabBarItem = UITabBarItem(
            title: "SmartHeat Calc",
            image: UIImage(systemName: "function"),
            selectedImage: UIImage(systemName: "function")
        )
        
        // Set view controllers
        viewControllers = [heatScanNavController, smartCalcNavController]
        
        // Select first tab by default
        selectedIndex = 0
    }
}
