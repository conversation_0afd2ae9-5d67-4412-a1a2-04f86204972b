//
//  AppDelegate.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        // Override point for customization after application launch.
        return true
    }

   


}

