//
//  HeatScanRecord.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import Foundation
import UIKit

// MARK: - Enums

enum PanType: String, CaseIterable {
    case ironPan = "iron_pan"
    case nonStickPan = "non_stick_pan"
    case wokPan = "wok_pan"
    case stainlessSteelPan = "stainless_steel_pan"
    case carbonSteelPan = "carbon_steel_pan"
    case castIronPan = "cast_iron_pan"
    case copperPan = "copper_pan"
    case aluminumPan = "aluminum_pan"
    
    var displayName: String {
        switch self {
        case .ironPan:
            return "Iron Pan"
        case .nonStickPan:
            return "Non-Stick Pan"
        case .wokPan:
            return "Wok Pan"
        case .stainlessSteelPan:
            return "Stainless Steel Pan"
        case .carbonSteelPan:
            return "Carbon Steel Pan"
        case .castIronPan:
            return "Cast Iron Pan"
        case .copperPan:
            return "Copper Pan"
        case .aluminumPan:
            return "Aluminum Pan"
        }
    }
    
    var icon: String {
        switch self {
        case .ironPan, .castIronPan:
            return "🍳"
        case .nonStickPan:
            return "🥘"
        case .wokPan:
            return "🥢"
        case .stainlessSteelPan:
            return "🍳"
        case .carbonSteelPan:
            return "🍳"
        case .copperPan:
            return "🟤"
        case .aluminumPan:
            return "⚪"
        }
    }
}

enum HeatSource: String, CaseIterable {
    case gas = "gas"
    case induction = "induction"
    case electric = "electric"
    case openFlame = "open_flame"
    case ceramic = "ceramic"
    case halogen = "halogen"
    
    var displayName: String {
        switch self {
        case .gas:
            return "Gas Stove"
        case .induction:
            return "Induction Cooktop"
        case .electric:
            return "Electric Stove"
        case .openFlame:
            return "Open Flame"
        case .ceramic:
            return "Ceramic Cooktop"
        case .halogen:
            return "Halogen Cooktop"
        }
    }
    
    var icon: String {
        switch self {
        case .gas:
            return "🔥"
        case .induction:
            return "⚡"
        case .electric:
            return "🔌"
        case .openFlame:
            return "🔥"
        case .ceramic:
            return "🟫"
        case .halogen:
            return "💡"
        }
    }
}

enum HeatDistribution: Int, CaseIterable {
    case poor = 1
    case fair = 2
    case good = 3
    case excellent = 4
    case perfect = 5
    
    var displayName: String {
        switch self {
        case .poor:
            return "Poor"
        case .fair:
            return "Fair"
        case .good:
            return "Good"
        case .excellent:
            return "Excellent"
        case .perfect:
            return "Perfect"
        }
    }
    
    var color: UIColor {
        switch self {
        case .poor:
            return AppTheme.Colors.error
        case .fair:
            return AppTheme.Colors.warning
        case .good:
            return AppTheme.Colors.secondary
        case .excellent:
            return AppTheme.Colors.primary
        case .perfect:
            return AppTheme.Colors.success
        }
    }
}

// MARK: - Temperature Data

struct TemperatureReading {
    let location: String // "center", "edge", "corner"
    let temperature: Double // in Celsius
    let timestamp: Date
    
    init(location: String, temperature: Double, timestamp: Date = Date()) {
        self.location = location
        self.temperature = temperature
        self.timestamp = timestamp
    }
}

// MARK: - Heat Scan Record

struct HeatScanRecord {
    let id: UUID
    let title: String
    let panType: PanType
    let heatSource: HeatSource
    let createdAt: Date
    let updatedAt: Date
    
    // Temperature and heating data
    let initialTemperature: Double // in Celsius
    let targetTemperature: Double // in Celsius
    let heatingTime: TimeInterval // seconds to reach target temperature
    let temperatureReadings: [TemperatureReading]
    
    // Heat distribution assessment
    let heatDistribution: HeatDistribution
    let centerHotSpot: Bool // true if center gets hottest
    let edgeHotSpot: Bool // true if edges get hottest
    
    // Performance metrics
    let heatingEfficiency: Double // 1.0 to 5.0 scale
    let temperatureConsistency: Double // 1.0 to 5.0 scale
    
    // Cooking results
    let burnSpots: Bool
    let unevenCooking: Bool
    let cookingSuccess: Bool
    
    // Notes and media
    let notes: String?
    let imagePaths: [String] // paths to thermal images or photos
    
    // Maintenance tracking
    let panCondition: String? // "new", "good", "worn", "damaged"
    let maintenanceNotes: String?
    
    init(
        id: UUID = UUID(),
        title: String,
        panType: PanType,
        heatSource: HeatSource,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        initialTemperature: Double,
        targetTemperature: Double,
        heatingTime: TimeInterval,
        temperatureReadings: [TemperatureReading] = [],
        heatDistribution: HeatDistribution,
        centerHotSpot: Bool = false,
        edgeHotSpot: Bool = false,
        heatingEfficiency: Double,
        temperatureConsistency: Double,
        burnSpots: Bool = false,
        unevenCooking: Bool = false,
        cookingSuccess: Bool = true,
        notes: String? = nil,
        imagePaths: [String] = [],
        panCondition: String? = nil,
        maintenanceNotes: String? = nil
    ) {
        self.id = id
        self.title = title
        self.panType = panType
        self.heatSource = heatSource
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.initialTemperature = initialTemperature
        self.targetTemperature = targetTemperature
        self.heatingTime = heatingTime
        self.temperatureReadings = temperatureReadings
        self.heatDistribution = heatDistribution
        self.centerHotSpot = centerHotSpot
        self.edgeHotSpot = edgeHotSpot
        self.heatingEfficiency = heatingEfficiency
        self.temperatureConsistency = temperatureConsistency
        self.burnSpots = burnSpots
        self.unevenCooking = unevenCooking
        self.cookingSuccess = cookingSuccess
        self.notes = notes
        self.imagePaths = imagePaths
        self.panCondition = panCondition
        self.maintenanceNotes = maintenanceNotes
    }
}

// MARK: - Extensions

extension HeatScanRecord {
    var averageTemperature: Double {
        guard !temperatureReadings.isEmpty else { return 0 }
        return temperatureReadings.reduce(0) { $0 + $1.temperature } / Double(temperatureReadings.count)
    }
    
    var temperatureRange: Double {
        guard !temperatureReadings.isEmpty else { return 0 }
        let temperatures = temperatureReadings.map { $0.temperature }
        return (temperatures.max() ?? 0) - (temperatures.min() ?? 0)
    }
    
    var overallRating: Double {
        return (heatingEfficiency + temperatureConsistency + Double(heatDistribution.rawValue)) / 3.0
    }
    
    var formattedHeatingTime: String {
        let minutes = Int(heatingTime) / 60
        let seconds = Int(heatingTime) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}
