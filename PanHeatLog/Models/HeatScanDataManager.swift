//
//  HeatScanDataManager.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import Foundation

class HeatScanDataManager {
    static let shared = HeatScanDataManager()
    
    private let userDefaults = UserDefaults.standard
    private let recordsKey = "heat_scan_records"
    
    private init() {}
    
    // MARK: - Data Storage
    
    func saveRecord(_ record: HeatScanRecord) {
        var records = getAllRecords()
        
        // Update existing record or add new one
        if let index = records.firstIndex(where: { $0.id == record.id }) {
            records[index] = record
        } else {
            records.append(record)
        }
        
        saveRecords(records)
    }
    
    func deleteRecord(withId id: UUID) {
        var records = getAllRecords()
        records.removeAll { $0.id == id }
        saveRecords(records)
    }
    
    func getAllRecords() -> [HeatScanRecord] {
        guard let data = userDefaults.data(forKey: recordsKey) else {
            return generateSampleData()
        }
        
        do {
            let records = try JSONDecoder().decode([HeatScanRecordCodable].self, from: data)
            return records.map { $0.toHeatScanRecord() }
        } catch {
            print("Error decoding records: \(error)")
            return generateSampleData()
        }
    }
    
    func getRecord(withId id: UUID) -> HeatScanRecord? {
        return getAllRecords().first { $0.id == id }
    }
    
    private func saveRecords(_ records: [HeatScanRecord]) {
        do {
            let codableRecords = records.map { HeatScanRecordCodable(from: $0) }
            let data = try JSONEncoder().encode(codableRecords)
            userDefaults.set(data, forKey: recordsKey)
        } catch {
            print("Error encoding records: \(error)")
        }
    }
    
    // MARK: - Filtering and Sorting
    
    func getRecords(filteredBy panType: PanType? = nil, heatSource: HeatSource? = nil) -> [HeatScanRecord] {
        var records = getAllRecords()
        
        if let panType = panType {
            records = records.filter { $0.panType == panType }
        }
        
        if let heatSource = heatSource {
            records = records.filter { $0.heatSource == heatSource }
        }
        
        return records.sorted { $0.createdAt > $1.createdAt }
    }
    
    func searchRecords(query: String) -> [HeatScanRecord] {
        let lowercaseQuery = query.lowercased()
        return getAllRecords().filter { record in
            record.title.lowercased().contains(lowercaseQuery) ||
            record.panType.displayName.lowercased().contains(lowercaseQuery) ||
            record.heatSource.displayName.lowercased().contains(lowercaseQuery) ||
            (record.notes?.lowercased().contains(lowercaseQuery) ?? false)
        }
    }
    
    // MARK: - Statistics
    
    func getStatistics() -> HeatScanStatistics {
        let records = getAllRecords()
        
        let totalRecords = records.count
        let averageRating = records.isEmpty ? 0 : records.reduce(0) { $0 + $1.overallRating } / Double(totalRecords)
        
        let panTypeStats = Dictionary(grouping: records, by: { $0.panType })
            .mapValues { $0.count }
        
        let heatSourceStats = Dictionary(grouping: records, by: { $0.heatSource })
            .mapValues { $0.count }
        
        let successRate = records.isEmpty ? 0 : Double(records.filter { $0.cookingSuccess }.count) / Double(totalRecords)
        
        return HeatScanStatistics(
            totalRecords: totalRecords,
            averageRating: averageRating,
            panTypeDistribution: panTypeStats,
            heatSourceDistribution: heatSourceStats,
            successRate: successRate
        )
    }
    
    // MARK: - Sample Data
    
    private func generateSampleData() -> [HeatScanRecord] {
        let sampleRecords = [
            HeatScanRecord(
                title: "Cast Iron on Gas - Morning Eggs",
                panType: .castIronPan,
                heatSource: .gas,
                createdAt: Date().addingTimeInterval(-86400 * 2), // 2 days ago
                initialTemperature: 20.0,
                targetTemperature: 180.0,
                heatingTime: 240.0, // 4 minutes
                temperatureReadings: [
                    TemperatureReading(location: "center", temperature: 185.0),
                    TemperatureReading(location: "edge", temperature: 175.0),
                    TemperatureReading(location: "corner", temperature: 165.0)
                ],
                heatDistribution: .good,
                centerHotSpot: true,
                heatingEfficiency: 4.0,
                temperatureConsistency: 3.5,
                cookingSuccess: true,
                notes: "Good even heating, slight hot spot in center. Perfect for eggs."
            ),
            
            HeatScanRecord(
                title: "Non-Stick on Induction - Pancakes",
                panType: .nonStickPan,
                heatSource: .induction,
                createdAt: Date().addingTimeInterval(-86400), // 1 day ago
                initialTemperature: 22.0,
                targetTemperature: 160.0,
                heatingTime: 120.0, // 2 minutes
                temperatureReadings: [
                    TemperatureReading(location: "center", temperature: 162.0),
                    TemperatureReading(location: "edge", temperature: 158.0),
                    TemperatureReading(location: "corner", temperature: 155.0)
                ],
                heatDistribution: .excellent,
                heatingEfficiency: 4.5,
                temperatureConsistency: 4.2,
                cookingSuccess: true,
                notes: "Excellent heat distribution. Very consistent temperature across surface."
            ),
            
            HeatScanRecord(
                title: "Stainless Steel Wok Test",
                panType: .wokPan,
                heatSource: .gas,
                createdAt: Date().addingTimeInterval(-3600), // 1 hour ago
                initialTemperature: 25.0,
                targetTemperature: 220.0,
                heatingTime: 180.0, // 3 minutes
                temperatureReadings: [
                    TemperatureReading(location: "center", temperature: 225.0),
                    TemperatureReading(location: "edge", temperature: 200.0),
                    TemperatureReading(location: "corner", temperature: 180.0)
                ],
                heatDistribution: .fair,
                centerHotSpot: true,
                heatingEfficiency: 3.5,
                temperatureConsistency: 2.8,
                burnSpots: true,
                unevenCooking: true,
                cookingSuccess: false,
                notes: "Significant hot spot in center. Burned food in middle while edges were undercooked."
            )
        ]
        
        // Save sample data
        saveRecords(sampleRecords)
        return sampleRecords
    }
}

// MARK: - Statistics Model

struct HeatScanStatistics {
    let totalRecords: Int
    let averageRating: Double
    let panTypeDistribution: [PanType: Int]
    let heatSourceDistribution: [HeatSource: Int]
    let successRate: Double
}

// MARK: - Codable Models for Persistence

struct TemperatureReadingCodable: Codable {
    let location: String
    let temperature: Double
    let timestamp: Date

    init(from reading: TemperatureReading) {
        self.location = reading.location
        self.temperature = reading.temperature
        self.timestamp = reading.timestamp
    }

    func toTemperatureReading() -> TemperatureReading {
        return TemperatureReading(location: location, temperature: temperature, timestamp: timestamp)
    }
}

struct HeatScanRecordCodable: Codable {
    let id: UUID
    let title: String
    let panType: String
    let heatSource: String
    let createdAt: Date
    let updatedAt: Date
    let initialTemperature: Double
    let targetTemperature: Double
    let heatingTime: TimeInterval
    let temperatureReadings: [TemperatureReadingCodable]
    let heatDistribution: Int
    let centerHotSpot: Bool
    let edgeHotSpot: Bool
    let heatingEfficiency: Double
    let temperatureConsistency: Double
    let burnSpots: Bool
    let unevenCooking: Bool
    let cookingSuccess: Bool
    let notes: String?
    let imagePaths: [String]
    let panCondition: String?
    let maintenanceNotes: String?

    init(from record: HeatScanRecord) {
        self.id = record.id
        self.title = record.title
        self.panType = record.panType.rawValue
        self.heatSource = record.heatSource.rawValue
        self.createdAt = record.createdAt
        self.updatedAt = record.updatedAt
        self.initialTemperature = record.initialTemperature
        self.targetTemperature = record.targetTemperature
        self.heatingTime = record.heatingTime
        self.temperatureReadings = record.temperatureReadings.map { TemperatureReadingCodable(from: $0) }
        self.heatDistribution = record.heatDistribution.rawValue
        self.centerHotSpot = record.centerHotSpot
        self.edgeHotSpot = record.edgeHotSpot
        self.heatingEfficiency = record.heatingEfficiency
        self.temperatureConsistency = record.temperatureConsistency
        self.burnSpots = record.burnSpots
        self.unevenCooking = record.unevenCooking
        self.cookingSuccess = record.cookingSuccess
        self.notes = record.notes
        self.imagePaths = record.imagePaths
        self.panCondition = record.panCondition
        self.maintenanceNotes = record.maintenanceNotes
    }

    func toHeatScanRecord() -> HeatScanRecord {
        return HeatScanRecord(
            id: id,
            title: title,
            panType: PanType(rawValue: panType) ?? .ironPan,
            heatSource: HeatSource(rawValue: heatSource) ?? .gas,
            createdAt: createdAt,
            updatedAt: updatedAt,
            initialTemperature: initialTemperature,
            targetTemperature: targetTemperature,
            heatingTime: heatingTime,
            temperatureReadings: temperatureReadings.map { $0.toTemperatureReading() },
            heatDistribution: HeatDistribution(rawValue: heatDistribution) ?? .good,
            centerHotSpot: centerHotSpot,
            edgeHotSpot: edgeHotSpot,
            heatingEfficiency: heatingEfficiency,
            temperatureConsistency: temperatureConsistency,
            burnSpots: burnSpots,
            unevenCooking: unevenCooking,
            cookingSuccess: cookingSuccess,
            notes: notes,
            imagePaths: imagePaths,
            panCondition: panCondition,
            maintenanceNotes: maintenanceNotes
        )
    }
}
