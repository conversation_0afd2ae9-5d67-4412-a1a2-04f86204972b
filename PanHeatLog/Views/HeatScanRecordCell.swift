//
//  HeatScanRecordCell.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class HeatScanRecordCell: UITableViewCell {
    
    static let identifier = "HeatScanRecordCell"
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let panTypeLabel = UILabel()
    private let heatSourceLabel = UILabel()
    private let ratingView = UIView()
    private let ratingLabel = UILabel()
    private let dateLabel = UILabel()
    private let temperatureLabel = UILabel()
    private let successIndicator = UIView()
    private let chevronImageView = UIImageView()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        setupContainerView()
        setupLabels()
        setupRatingView()
        setupSuccessIndicator()
        setupChevron()
        setupConstraints()
    }
    
    private func setupContainerView() {
        containerView.applyCardStyle()
        contentView.addSubview(containerView)
    }
    
    private func setupLabels() {
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.numberOfLines = 1
        
        subtitleLabel.font = AppTheme.Typography.subheadline
        subtitleLabel.textColor = AppTheme.Colors.secondaryText
        subtitleLabel.numberOfLines = 1
        
        panTypeLabel.font = AppTheme.Typography.caption1
        panTypeLabel.textColor = AppTheme.Colors.primary
        panTypeLabel.backgroundColor = AppTheme.Colors.primary.withAlphaComponent(0.1)
        panTypeLabel.layer.cornerRadius = 8
        panTypeLabel.textAlignment = .center
        panTypeLabel.layer.masksToBounds = true
        
        heatSourceLabel.font = AppTheme.Typography.caption1
        heatSourceLabel.textColor = AppTheme.Colors.secondary
        heatSourceLabel.backgroundColor = AppTheme.Colors.secondary.withAlphaComponent(0.1)
        heatSourceLabel.layer.cornerRadius = 8
        heatSourceLabel.textAlignment = .center
        heatSourceLabel.layer.masksToBounds = true
        
        dateLabel.font = AppTheme.Typography.caption2
        dateLabel.textColor = AppTheme.Colors.tertiaryText
        dateLabel.textAlignment = .right
        
        temperatureLabel.font = AppTheme.Typography.caption1
        temperatureLabel.textColor = AppTheme.Colors.secondaryText
        temperatureLabel.textAlignment = .right
        
        [titleLabel, subtitleLabel, panTypeLabel, heatSourceLabel, dateLabel, temperatureLabel].forEach {
            containerView.addSubview($0)
        }
    }
    
    private func setupRatingView() {
        ratingView.backgroundColor = AppTheme.Colors.accent
        ratingView.layer.cornerRadius = 12
        
        ratingLabel.font = AppTheme.Typography.caption1.withWeight(.bold)
        ratingLabel.textColor = .white
        ratingLabel.textAlignment = .center
        
        containerView.addSubview(ratingView)
        ratingView.addSubview(ratingLabel)
    }
    
    private func setupSuccessIndicator() {
        successIndicator.layer.cornerRadius = 4
        containerView.addSubview(successIndicator)
    }
    
    private func setupChevron() {
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = AppTheme.Colors.tertiaryText
        chevronImageView.contentMode = .scaleAspectFit
        containerView.addSubview(chevronImageView)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(AppTheme.Spacing.xs)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.equalToSuperview().offset(AppTheme.Spacing.md)
            make.trailing.equalTo(ratingView.snp.leading).offset(-AppTheme.Spacing.sm)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.xs)
            make.leading.equalTo(titleLabel)
            make.trailing.equalTo(titleLabel)
        }
        
        panTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.equalTo(titleLabel)
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(60)
        }
        
        heatSourceLabel.snp.makeConstraints { make in
            make.top.equalTo(panTypeLabel)
            make.leading.equalTo(panTypeLabel.snp.trailing).offset(AppTheme.Spacing.xs)
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(60)
        }
        
        temperatureLabel.snp.makeConstraints { make in
            make.top.equalTo(panTypeLabel)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-AppTheme.Spacing.sm)
            make.height.equalTo(20)
        }
        
        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(temperatureLabel.snp.bottom).offset(AppTheme.Spacing.xs)
            make.trailing.equalTo(temperatureLabel)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.md)
        }
        
        ratingView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-AppTheme.Spacing.sm)
            make.width.height.equalTo(24)
        }
        
        ratingLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        successIndicator.snp.makeConstraints { make in
            make.top.equalTo(ratingView.snp.bottom).offset(AppTheme.Spacing.xs)
            make.centerX.equalTo(ratingView)
            make.width.height.equalTo(8)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-AppTheme.Spacing.md)
            make.width.height.equalTo(16)
        }
    }
    
    // MARK: - Configuration
    
    func configure(with record: HeatScanRecord) {
        titleLabel.text = record.title
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        subtitleLabel.text = formatter.string(from: record.createdAt)
        
        panTypeLabel.text = " \(record.panType.icon) \(record.panType.displayName) "
        heatSourceLabel.text = " \(record.heatSource.icon) \(record.heatSource.displayName) "
        
        let timeFormatter = DateFormatter()
        timeFormatter.dateStyle = .none
        timeFormatter.timeStyle = .short
        dateLabel.text = timeFormatter.string(from: record.createdAt)
        
        temperatureLabel.text = String(format: "%.0f°C", record.targetTemperature)
        
        ratingLabel.text = String(format: "%.1f", record.overallRating)
        
        successIndicator.backgroundColor = record.cookingSuccess ? 
            AppTheme.Colors.success : AppTheme.Colors.error
        
        // Update rating view color based on rating
        let rating = record.overallRating
        if rating >= 4.0 {
            ratingView.backgroundColor = AppTheme.Colors.success
        } else if rating >= 3.0 {
            ratingView.backgroundColor = AppTheme.Colors.accent
        } else if rating >= 2.0 {
            ratingView.backgroundColor = AppTheme.Colors.warning
        } else {
            ratingView.backgroundColor = AppTheme.Colors.error
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = nil
        subtitleLabel.text = nil
        panTypeLabel.text = nil
        heatSourceLabel.text = nil
        dateLabel.text = nil
        temperatureLabel.text = nil
        ratingLabel.text = nil
    }
}
