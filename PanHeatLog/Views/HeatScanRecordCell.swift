//
//  HeatScanRecordCell.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class HeatScanRecordCell: UITableViewCell {

    static let identifier = "HeatScanRecordCell"

    // MARK: - UI Components

    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let dateLabel = UILabel()
    private let panTypeIconLabel = UILabel()
    private let panTypeLabel = UILabel()
    private let heatSourceIconLabel = UILabel()
    private let heatSourceLabel = UILabel()
    private let ratingView = UIView()
    private let ratingLabel = UILabel()
    private let temperatureView = UIView()
    private let temperatureIconLabel = UILabel()
    private let temperatureLabel = UILabel()
    private let timeView = UIView()
    private let timeIconLabel = UILabel()
    private let timeLabel = UILabel()
    private let successIndicator = UIView()
    private let chevronImageView = UIImageView()
    private let separatorLine = UIView()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        setupContainerView()
        setupLabels()
        setupRatingView()
        setupSuccessIndicator()
        setupChevron()
        setupSeparatorLine()
        setupConstraints()
    }
    
    private func setupContainerView() {
        containerView.applyCardStyle()
        contentView.addSubview(containerView)
    }
    
    private func setupLabels() {
        // Title
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.numberOfLines = 2

        // Date
        dateLabel.font = AppTheme.Typography.caption2
        dateLabel.textColor = AppTheme.Colors.tertiaryText
        dateLabel.textAlignment = .right

        // Pan type
        panTypeIconLabel.font = AppTheme.Typography.body
        panTypeIconLabel.textAlignment = .center

        panTypeLabel.font = AppTheme.Typography.caption1
        panTypeLabel.textColor = AppTheme.Colors.primaryText
        panTypeLabel.numberOfLines = 1

        // Heat source
        heatSourceIconLabel.font = AppTheme.Typography.body
        heatSourceIconLabel.textAlignment = .center

        heatSourceLabel.font = AppTheme.Typography.caption1
        heatSourceLabel.textColor = AppTheme.Colors.primaryText
        heatSourceLabel.numberOfLines = 1

        // Temperature
        temperatureIconLabel.text = "🌡️"
        temperatureIconLabel.font = AppTheme.Typography.caption1

        temperatureLabel.font = AppTheme.Typography.caption1
        temperatureLabel.textColor = AppTheme.Colors.secondaryText

        // Time
        timeIconLabel.text = "⏱️"
        timeIconLabel.font = AppTheme.Typography.caption1

        timeLabel.font = AppTheme.Typography.caption1
        timeLabel.textColor = AppTheme.Colors.secondaryText

        [titleLabel, dateLabel, panTypeIconLabel, panTypeLabel, heatSourceIconLabel,
         heatSourceLabel, temperatureIconLabel, temperatureLabel, timeIconLabel, timeLabel].forEach {
            containerView.addSubview($0)
        }
    }
    
    private func setupRatingView() {
        ratingView.backgroundColor = AppTheme.Colors.accent
        ratingView.layer.cornerRadius = 16

        ratingLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        ratingLabel.textColor = .white
        ratingLabel.textAlignment = .center

        containerView.addSubview(ratingView)
        ratingView.addSubview(ratingLabel)
    }

    private func setupSuccessIndicator() {
        successIndicator.layer.cornerRadius = 6
        containerView.addSubview(successIndicator)
    }

    private func setupChevron() {
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = AppTheme.Colors.tertiaryText
        chevronImageView.contentMode = .scaleAspectFit
        containerView.addSubview(chevronImageView)
    }

    private func setupSeparatorLine() {
        separatorLine.backgroundColor = AppTheme.Colors.border.withAlphaComponent(0.3)
        containerView.addSubview(separatorLine)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(AppTheme.Spacing.xs)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        // Top row: Title and Date
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.leading.equalToSuperview().offset(AppTheme.Spacing.md)
            make.trailing.equalTo(dateLabel.snp.leading).offset(-AppTheme.Spacing.sm)
        }

        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-AppTheme.Spacing.sm)
            make.width.greaterThanOrEqualTo(60)
        }

        // Second row: Pan type and Heat source
        panTypeIconLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.equalTo(titleLabel)
            make.width.equalTo(24)
        }

        panTypeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(panTypeIconLabel)
            make.leading.equalTo(panTypeIconLabel.snp.trailing).offset(AppTheme.Spacing.xs)
            make.width.lessThanOrEqualTo(100)
        }

        heatSourceIconLabel.snp.makeConstraints { make in
            make.centerY.equalTo(panTypeIconLabel)
            make.leading.equalTo(panTypeLabel.snp.trailing).offset(AppTheme.Spacing.md)
            make.width.equalTo(24)
        }

        heatSourceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(panTypeIconLabel)
            make.leading.equalTo(heatSourceIconLabel.snp.trailing).offset(AppTheme.Spacing.xs)
            make.trailing.lessThanOrEqualTo(ratingView.snp.leading).offset(-AppTheme.Spacing.sm)
        }

        // Third row: Temperature and Time
        temperatureIconLabel.snp.makeConstraints { make in
            make.top.equalTo(panTypeIconLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.equalTo(titleLabel)
            make.width.equalTo(20)
        }

        temperatureLabel.snp.makeConstraints { make in
            make.centerY.equalTo(temperatureIconLabel)
            make.leading.equalTo(temperatureIconLabel.snp.trailing).offset(AppTheme.Spacing.xs)
            make.width.greaterThanOrEqualTo(50)
        }

        timeIconLabel.snp.makeConstraints { make in
            make.centerY.equalTo(temperatureIconLabel)
            make.leading.equalTo(temperatureLabel.snp.trailing).offset(AppTheme.Spacing.md)
            make.width.equalTo(20)
        }

        timeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(temperatureIconLabel)
            make.leading.equalTo(timeIconLabel.snp.trailing).offset(AppTheme.Spacing.xs)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.md)
        }

        // Rating view
        ratingView.snp.makeConstraints { make in
            make.top.equalTo(40)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-AppTheme.Spacing.sm)
            make.width.height.equalTo(32)
        }

        ratingLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        // Success indicator
        successIndicator.snp.makeConstraints { make in
            make.top.equalTo(ratingView.snp.bottom).offset(AppTheme.Spacing.xs)
            make.centerX.equalTo(ratingView)
            make.width.height.equalTo(12)
        }

        // Chevron
        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-AppTheme.Spacing.md)
            make.width.height.equalTo(16)
        }

        // Separator line
        separatorLine.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.height.equalTo(0.5)
        }
    }
    
    // MARK: - Configuration

    func configure(with record: HeatScanRecord) {
        titleLabel.text = record.title

        // Date formatting
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        dateLabel.text = formatter.string(from: record.createdAt)

        // Pan type
        panTypeIconLabel.text = record.panType.icon
        panTypeLabel.text = record.panType.displayName

        // Heat source
        heatSourceIconLabel.text = record.heatSource.icon
        heatSourceLabel.text = record.heatSource.displayName

        // Temperature
        temperatureLabel.text = String(format: "%.0f°C", record.targetTemperature)

        // Time
        timeLabel.text = record.formattedHeatingTime

        // Rating
        ratingLabel.text = String(format: "%.1f", record.overallRating)

        // Success indicator
        successIndicator.backgroundColor = record.cookingSuccess ?
            AppTheme.Colors.success : AppTheme.Colors.error

        // Update rating view color based on rating
        let rating = record.overallRating
        if rating >= 4.0 {
            ratingView.backgroundColor = AppTheme.Colors.success
        } else if rating >= 3.0 {
            ratingView.backgroundColor = AppTheme.Colors.accent
        } else if rating >= 2.0 {
            ratingView.backgroundColor = AppTheme.Colors.warning
        } else {
            ratingView.backgroundColor = AppTheme.Colors.error
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = nil
        dateLabel.text = nil
        panTypeIconLabel.text = nil
        panTypeLabel.text = nil
        heatSourceIconLabel.text = nil
        heatSourceLabel.text = nil
        temperatureLabel.text = nil
        timeLabel.text = nil
        ratingLabel.text = nil
    }
}
