//
//  BaseTextField.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class BaseTextField: UIView {
    
    // MARK: - UI Components
    
    private let titleLabel = UILabel()
    private let textField = UITextField()
    private let errorLabel = UILabel()
    private let containerView = UIView()
    
    // MARK: - Properties
    
    var title: String? {
        didSet {
            titleLabel.text = title
            titleLabel.isHidden = title?.isEmpty ?? true
        }
    }
    
    var placeholder: String? {
        didSet {
            textField.placeholder = placeholder
        }
    }
    
    var text: String? {
        get { textField.text }
        set { textField.text = newValue }
    }
    
    var errorMessage: String? {
        didSet {
            errorLabel.text = errorMessage
            errorLabel.isHidden = errorMessage?.isEmpty ?? true
            updateBorderColor()
        }
    }
    
    var isRequired: Bool = false {
        didSet {
            updateTitleLabel()
        }
    }
    
    var keyboardType: UIKeyboardType {
        get { textField.keyboardType }
        set { textField.keyboardType = newValue }
    }
    
    var isSecureTextEntry: Bool {
        get { textField.isSecureTextEntry }
        set { textField.isSecureTextEntry = newValue }
    }
    
    var delegate: UITextFieldDelegate? {
        get { textField.delegate }
        set { textField.delegate = newValue }
    }
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        setupTitleLabel()
        setupContainerView()
        setupTextField()
        setupErrorLabel()
        setupConstraints()
    }
    
    private func setupTitleLabel() {
        titleLabel.font = AppTheme.Typography.subheadline
        titleLabel.textColor = AppTheme.Colors.primaryText
        titleLabel.isHidden = true
        addSubview(titleLabel)
    }
    
    private func setupContainerView() {
        containerView.backgroundColor = AppTheme.Colors.background
        containerView.layer.cornerRadius = AppTheme.CornerRadius.small
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = AppTheme.Colors.border.cgColor
        addSubview(containerView)
    }
    
    private func setupTextField() {
        textField.font = AppTheme.Typography.body
        textField.textColor = AppTheme.Colors.primaryText
        textField.tintColor = AppTheme.Colors.primary
        textField.borderStyle = .none
        textField.clearButtonMode = .whileEditing
        
        textField.addTarget(self, action: #selector(textFieldDidBeginEditing), for: .editingDidBegin)
        textField.addTarget(self, action: #selector(textFieldDidEndEditing), for: .editingDidEnd)
        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        
        containerView.addSubview(textField)
    }
    
    private func setupErrorLabel() {
        errorLabel.font = AppTheme.Typography.caption1
        errorLabel.textColor = AppTheme.Colors.error
        errorLabel.numberOfLines = 0
        errorLabel.isHidden = true
        addSubview(errorLabel)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.xs)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        
        textField.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.centerY.equalToSuperview()
        }
        
        errorLabel.snp.makeConstraints { make in
            make.top.equalTo(containerView.snp.bottom).offset(AppTheme.Spacing.xs)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Private Methods
    
    private func updateTitleLabel() {
        guard let title = title else { return }
        
        if isRequired {
            let attributedTitle = NSMutableAttributedString(string: title)
            let asterisk = NSAttributedString(
                string: " *",
                attributes: [.foregroundColor: AppTheme.Colors.error]
            )
            attributedTitle.append(asterisk)
            titleLabel.attributedText = attributedTitle
        } else {
            titleLabel.text = title
        }
    }
    
    private func updateBorderColor() {
        let hasError = !(errorMessage?.isEmpty ?? true)
        containerView.layer.borderColor = hasError ? 
            AppTheme.Colors.error.cgColor : 
            AppTheme.Colors.border.cgColor
    }
    
    // MARK: - Text Field Events
    
    @objc private func textFieldDidBeginEditing() {
        containerView.layer.borderColor = AppTheme.Colors.primary.cgColor
        errorMessage = nil
    }
    
    @objc private func textFieldDidEndEditing() {
        updateBorderColor()
    }
    
    @objc private func textFieldDidChange() {
        errorMessage = nil
    }
    
    // MARK: - Public Methods
    
    func validate() -> Bool {
        if isRequired && (text?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true) {
            errorMessage = "This field is required"
            return false
        }
        return true
    }
    
    @discardableResult
    override func becomeFirstResponder() -> Bool {
        return textField.becomeFirstResponder()
    }
    
    @discardableResult
    override func resignFirstResponder() -> Bool {
        return textField.resignFirstResponder()
    }
}
