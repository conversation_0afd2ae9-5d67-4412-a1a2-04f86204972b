//
//  BaseButton.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit
import SnapKit

class BaseButton: UIButton {
    
    enum ButtonStyle {
        case primary
        case secondary
        case outline
        case text
    }
    
    enum ButtonSize {
        case small
        case medium
        case large
    }
    
    private var buttonStyle: ButtonStyle = .primary
    private var buttonSize: ButtonSize = .medium
    
    // MARK: - Initialization
    
    init(style: ButtonStyle = .primary, size: ButtonSize = .medium) {
        self.buttonStyle = style
        self.buttonSize = size
        super.init(frame: .zero)
        setupButton()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupButton()
    }
    
    // MARK: - Setup
    
    private func setupButton() {
        titleLabel?.font = fontForSize()
        layer.cornerRadius = AppTheme.CornerRadius.small
        
        applyStyle()
        setupConstraints()
        
        // Add touch feedback
        addTarget(self, action: #selector(touchDown), for: .touchDown)
        addTarget(self, action: #selector(touchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupConstraints() {
        snp.makeConstraints { make in
            make.height.equalTo(heightForSize())
        }
    }
    
    private func applyStyle() {
        switch buttonStyle {
        case .primary:
            backgroundColor = AppTheme.Colors.primary
            setTitleColor(.white, for: .normal)
            setTitleColor(.white.withAlphaComponent(0.7), for: .highlighted)
            
        case .secondary:
            backgroundColor = AppTheme.Colors.secondary
            setTitleColor(.white, for: .normal)
            setTitleColor(.white.withAlphaComponent(0.7), for: .highlighted)
            
        case .outline:
            backgroundColor = .clear
            layer.borderWidth = 1.5
            layer.borderColor = AppTheme.Colors.primary.cgColor
            setTitleColor(AppTheme.Colors.primary, for: .normal)
            setTitleColor(AppTheme.Colors.primary.withAlphaComponent(0.7), for: .highlighted)
            
        case .text:
            backgroundColor = .clear
            setTitleColor(AppTheme.Colors.primary, for: .normal)
            setTitleColor(AppTheme.Colors.primary.withAlphaComponent(0.7), for: .highlighted)
        }
    }
    
    private func fontForSize() -> UIFont {
        switch buttonSize {
        case .small:
            return AppTheme.Typography.footnote
        case .medium:
            return AppTheme.Typography.callout
        case .large:
            return AppTheme.Typography.headline
        }
    }
    
    private func heightForSize() -> CGFloat {
        switch buttonSize {
        case .small:
            return 32
        case .medium:
            return 44
        case .large:
            return 56
        }
    }
    
    // MARK: - Touch Feedback
    
    @objc private func touchDown() {
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
    }
    
    @objc private func touchUp() {
        UIView.animate(withDuration: 0.1) {
            self.transform = .identity
        }
    }
    
    // MARK: - Public Methods
    
    func updateStyle(_ style: ButtonStyle) {
        self.buttonStyle = style
        applyStyle()
    }
    
    func updateSize(_ size: ButtonSize) {
        self.buttonSize = size
        titleLabel?.font = fontForSize()
        snp.updateConstraints { make in
            make.height.equalTo(heightForSize())
        }
    }
    
    override var isEnabled: Bool {
        didSet {
            alpha = isEnabled ? 1.0 : 0.6
        }
    }
}
