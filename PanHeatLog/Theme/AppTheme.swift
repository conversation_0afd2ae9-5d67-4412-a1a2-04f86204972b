//
//  AppTheme.swift
//  PanHeatLog
//
//  Created by jj on 2025/6/29.
//

import UIKit

struct AppTheme {
    
    // MARK: - Colors
    struct Colors {
        // Primary theme colors - Heat/Fire inspired
        static let primary = UIColor(red: 0.95, green: 0.35, blue: 0.20, alpha: 1.0)        // Flame Red
        static let primaryDark = UIColor(red: 0.80, green: 0.25, blue: 0.10, alpha: 1.0)   // Dark Red
        static let secondary = UIColor(red: 1.0, green: 0.65, blue: 0.20, alpha: 1.0)      // Warm Orange
        static let accent = UIColor(red: 0.95, green: 0.85, blue: 0.30, alpha: 1.0)        // Golden Yellow
        
        // Background colors
        static let background = UIColor.systemBackground
        static let secondaryBackground = UIColor.secondarySystemBackground
        static let tertiaryBackground = UIColor.tertiarySystemBackground
        
        // Text colors
        static let primaryText = UIColor.label
        static let secondaryText = UIColor.secondaryLabel
        static let tertiaryText = UIColor.tertiaryLabel
        
        // System colors
        static let success = UIColor.systemGreen
        static let warning = UIColor.systemOrange
        static let error = UIColor.systemRed
        static let info = UIColor.systemBlue
        
        // Card and border colors
        static let cardBackground = UIColor.systemBackground
        static let border = UIColor.separator
        static let shadow = UIColor.black.withAlphaComponent(0.1)
    }
    
    // MARK: - Typography
    struct Typography {
        static let largeTitle = UIFont.systemFont(ofSize: 34, weight: .bold)
        static let title1 = UIFont.systemFont(ofSize: 28, weight: .bold)
        static let title2 = UIFont.systemFont(ofSize: 22, weight: .bold)
        static let title3 = UIFont.systemFont(ofSize: 20, weight: .semibold)
        static let headline = UIFont.systemFont(ofSize: 17, weight: .semibold)
        static let body = UIFont.systemFont(ofSize: 17, weight: .regular)
        static let callout = UIFont.systemFont(ofSize: 16, weight: .regular)
        static let subheadline = UIFont.systemFont(ofSize: 15, weight: .regular)
        static let footnote = UIFont.systemFont(ofSize: 13, weight: .regular)
        static let caption1 = UIFont.systemFont(ofSize: 12, weight: .regular)
        static let caption2 = UIFont.systemFont(ofSize: 11, weight: .regular)
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }
    
    // MARK: - Shadow
    struct Shadow {
        static let small = ShadowStyle(
            color: Colors.shadow,
            offset: CGSize(width: 0, height: 2),
            radius: 4,
            opacity: 0.1
        )
        
        static let medium = ShadowStyle(
            color: Colors.shadow,
            offset: CGSize(width: 0, height: 4),
            radius: 8,
            opacity: 0.15
        )
        
        static let large = ShadowStyle(
            color: Colors.shadow,
            offset: CGSize(width: 0, height: 8),
            radius: 16,
            opacity: 0.2
        )
    }
    
    struct ShadowStyle {
        let color: UIColor
        let offset: CGSize
        let radius: CGFloat
        let opacity: Float
    }
}

// MARK: - Theme Configuration Methods
extension AppTheme {
    
    static func configureNavigationController(_ navigationController: UINavigationController) {
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = Colors.primary
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: Typography.headline
        ]
        appearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: Typography.largeTitle
        ]
        
        navigationController.navigationBar.standardAppearance = appearance
        navigationController.navigationBar.scrollEdgeAppearance = appearance
        navigationController.navigationBar.compactAppearance = appearance
        navigationController.navigationBar.tintColor = UIColor.white
        navigationController.navigationBar.prefersLargeTitles = true
    }
    
    static func configureTabBar(_ tabBar: UITabBar) {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = Colors.background
        
        tabBar.standardAppearance = appearance
        tabBar.scrollEdgeAppearance = appearance
        tabBar.tintColor = Colors.primary
        tabBar.unselectedItemTintColor = Colors.secondaryText
    }
}

// MARK: - UIView Extensions for Theme
extension UIView {
    
    func applyShadow(_ shadowStyle: AppTheme.ShadowStyle) {
        layer.shadowColor = shadowStyle.color.cgColor
        layer.shadowOffset = shadowStyle.offset
        layer.shadowRadius = shadowStyle.radius
        layer.shadowOpacity = shadowStyle.opacity
        layer.masksToBounds = false
    }
    
    func applyCardStyle() {
        backgroundColor = AppTheme.Colors.cardBackground
        layer.cornerRadius = AppTheme.CornerRadius.medium
        applyShadow(AppTheme.Shadow.small)
    }
}
